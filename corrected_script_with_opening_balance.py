import pandas as pd
from datetime import date
import os
import logging
from databricks import sql

# --- Configuration ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        if not all([server_hostname, http_path, access_token]):
            raise ValueError("Missing one or more Databricks connection variables.")
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_databricks_query(query: str, connection=None) -> pd.DataFrame:
    """Execute a SQL query on Databricks and return results as a DataFrame."""
    close_conn = False
    if connection is None:
        connection = get_databricks_connection()
        close_conn = True
    try:
        logging.info("Executing query on Databricks...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        logging.info(f"Query successful. Fetched {len(data)} rows.")
        return pd.DataFrame(data, columns=columns)
    finally:
        if close_conn and connection:
            connection.close()
            logging.info("Databricks connection closed.")

def load_material_category_mappings():
    """Load material category mappings from the Excel file."""
    try:
        # First try to load from the CSV we created
        if os.path.exists('material_category_mappings.csv'):
            logging.info("Loading material categories from CSV file...")
            category_df = pd.read_csv('material_category_mappings.csv')
            logging.info(f"Loaded {len(category_df)} material category mappings from CSV")
            return category_df
        
        # Otherwise load from Excel
        excel_file = 'DE Temp POCM Calculation P07 2025_TL.xlsx'
        if os.path.exists(excel_file):
            logging.info("Loading material categories from Excel file...")
            category_df = pd.read_excel(excel_file, sheet_name='Glass and others')
            category_df.columns = ['Material', 'Category']
            category_df = category_df.dropna().drop_duplicates(subset=['Material'])
            logging.info(f"Loaded {len(category_df)} material category mappings from Excel")
            return category_df
        else:
            logging.warning("No material category mapping file found")
            return pd.DataFrame(columns=['Material', 'Category'])
            
    except Exception as e:
        logging.error(f"Error loading material category mappings: {e}")
        return pd.DataFrame(columns=['Material', 'Category'])

def get_corrected_stock_query():
    """Returns the corrected SQL query using opening balance + movements methodology."""
    
    # Load material categories to build the dynamic query
    category_df = load_material_category_mappings()
    
    if category_df.empty:
        logging.warning("No material categories loaded - using default classification")
        glass_materials_list = "7546978, 7546979, 7546980, 7546988, 7546989"  # Default sample
    else:
        glass_materials = category_df[category_df['Category'].str.strip() == 'Glass']['Material'].tolist()
        glass_materials_list = ', '.join(map(str, glass_materials))
        logging.info(f"Found {len(glass_materials)} Glass materials")
    
    return f"""
    -- CORRECTED SAP STOCK CALCULATION QUERY
    -- Methodology: Opening Balance + Receipts - Issues = Closing Stock
    -- Different date ranges based on Material Category (Glass vs Others)
    
    WITH MaterialCategories AS (
        SELECT 
            matnr AS Material,
            CASE 
                WHEN matnr IN ({glass_materials_list}) THEN 'Glass'
                ELSE 'Others'
            END AS Category,
            CASE 
                WHEN matnr IN ({glass_materials_list}) THEN '2024-11-01'  -- Glass: Start from Nov 1, 2024
                ELSE '2022-08-31'    -- Others: Start from Aug 31, 2022
            END AS StartDate
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
        WHERE werks LIKE 'DE%'
          AND matnr BETWEEN 7546978 AND 7597095
    ),
    
    OpeningBalance AS (
        -- Calculate opening balance: Sum all movements from beginning up to start date
        SELECT 
            mc.Material,
            mc.Category,
            mc.StartDate,
            mseg.werks AS Plant,
            mseg.lgort AS StorageLocation,
            SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS OpeningBalance
        FROM MaterialCategories mc
        JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg mseg
            ON mc.Material = mseg.matnr
        WHERE mseg.werks LIKE 'DE%'
          AND mseg.cpudt_mkpf < mc.StartDate
        GROUP BY mc.Material, mc.Category, mc.StartDate, mseg.werks, mseg.lgort
    ),
    
    MovementsAfterStart AS (
        -- Calculate receipts and issues after the start date
        SELECT 
            mc.Material,
            mc.Category,
            mc.StartDate,
            mseg.werks AS Plant,
            mseg.lgort AS StorageLocation,
            SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE 0 END) AS Receipts,
            SUM(CASE WHEN mseg.shkzg = 'H' THEN mseg.menge ELSE 0 END) AS Issues,
            SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS NetMovements
        FROM MaterialCategories mc
        JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg mseg
            ON mc.Material = mseg.matnr
        WHERE mseg.werks LIKE 'DE%'
          AND mseg.cpudt_mkpf >= mc.StartDate
        GROUP BY mc.Material, mc.Category, mc.StartDate, mseg.werks, mseg.lgort
    ),
    
    CalculatedStock AS (
        -- Calculate final closing stock: Opening Balance + Net Movements
        SELECT 
            mc.Material AS SKU,
            mc.Category,
            mc.StartDate,
            COALESCE(ob.Plant, mas.Plant) AS Plant,
            COALESCE(ob.StorageLocation, mas.StorageLocation) AS StorageLocation,
            COALESCE(ob.OpeningBalance, 0) AS OpeningBalance,
            COALESCE(mas.Receipts, 0) AS Receipts,
            COALESCE(mas.Issues, 0) AS Issues,
            COALESCE(mas.NetMovements, 0) AS NetMovements,
            COALESCE(ob.OpeningBalance, 0) + COALESCE(mas.NetMovements, 0) AS CalculatedClosingStock
        FROM MaterialCategories mc
        FULL OUTER JOIN OpeningBalance ob ON mc.Material = ob.Material
        FULL OUTER JOIN MovementsAfterStart mas ON mc.Material = mas.Material 
            AND COALESCE(ob.Plant, 'NULL') = COALESCE(mas.Plant, 'NULL')
            AND COALESCE(ob.StorageLocation, 'NULL') = COALESCE(mas.StorageLocation, 'NULL')
    ),
    
    MaterialInfo AS (
        SELECT 
            mard.matnr AS SKU,
            mard.werks AS Plant,
            makt.maktx AS MaterialDescription,
            mbew.verpr AS MovingPrice,
            mbew.peinh AS PriceUnit,
            mard.labst AS CurrentMARDStock
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard AS mard
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS mbew
            ON mard.matnr = mbew.matnr AND mard.werks = mbew.bwkey
        LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS makt
            ON mard.matnr = makt.matnr AND makt.spras = 'E'
        WHERE mard.werks LIKE 'DE%'
          AND mard.matnr BETWEEN 7546978 AND 7597095
    )
    
    -- Final result with corrected stock calculation
    SELECT 
        'DE' AS Country,
        cs.SKU,
        mi.MaterialDescription,
        cs.StorageLocation,
        cs.Plant,
        cs.Category,
        cs.StartDate,
        cs.OpeningBalance,
        cs.Receipts,
        cs.Issues,
        cs.NetMovements,
        cs.CalculatedClosingStock AS TotalStock,  -- CORRECTED STOCK QUANTITY
        mi.CurrentMARDStock AS OldMARDStock,      -- For comparison
        cs.CalculatedClosingStock - mi.CurrentMARDStock AS StockDifference,
        mi.MovingPrice / NULLIF(mi.PriceUnit, 0) AS Price,
        cs.CalculatedClosingStock * (mi.MovingPrice / NULLIF(mi.PriceUnit, 0)) AS TotalStockValue,
        CAST('2025-07-31' AS DATE) AS Today
    FROM CalculatedStock cs
    LEFT JOIN MaterialInfo mi ON cs.SKU = mi.SKU AND cs.Plant = mi.Plant
    WHERE cs.CalculatedClosingStock > 0
    ORDER BY cs.Plant, cs.StorageLocation, cs.SKU;
    """

def determine_status(category, months):
    """Determine status based on impairment category and months aged."""
    category_lower = str(category).lower()
    
    if category_lower == 'glass':
        if months >= 36: return 'Obsolete'
        if months >= 10: return 'Alarm'
        if months >= 3: return 'Slow Mover'
        return 'Fast Mover'
    else: # Default for 'others' and any other category
        if months >= 9: return 'Obsolete'
        if months >= 6: return 'Alarm'
        if months >= 3: return 'Slow Mover'
        return 'Fast Mover'

def process_corrected_data(df):
    """Process the corrected stock data for Power BI output."""
    if df is None or df.empty:
        logging.warning("Input DataFrame is empty. Skipping processing.")
        return pd.DataFrame()

    logging.info("Processing corrected stock data...")
    
    # Add time-based calculations
    df['Today'] = pd.to_datetime(df['Today'])
    df['Days'] = 0  # Will be calculated based on movement dates if needed
    df['Months'] = 0  # Will be calculated based on movement dates if needed
    df['Week_Num'] = 'W' + df['Today'].dt.isocalendar().week.astype(str).str.zfill(2)
    
    # Add status based on category (using the corrected category from the query)
    df['Status'] = df.apply(lambda row: determine_status(row['Category'], row['Months']), axis=1)
    
    # Rename columns to match expected output format
    df.rename(columns={
        'MaterialDescription': 'Material Description',
        'StorageLocation': 'Storage Location',
        'TotalStock': 'Total Stock',
        'TotalStockValue': 'Total Value',
        'Category': 'Impairment Category'
    }, inplace=True)
    
    # Select final columns
    final_columns = [
        'Country', 'SKU', 'Material Description', 'Storage Location', 'Plant',
        'Impairment Category', 'StartDate', 'OpeningBalance', 'Receipts', 'Issues',
        'Total Stock', 'OldMARDStock', 'StockDifference', 'Price', 'Total Value',
        'Status', 'Today', 'Week_Num'
    ]
    
    existing_columns = [col for col in final_columns if col in df.columns]
    processed_df = df[existing_columns]
    
    logging.info("Corrected data processing complete.")
    return processed_df

if __name__ == '__main__':
    try:
        logging.info("=== RUNNING CORRECTED STOCK CALCULATION ===")
        logging.info("Using Opening Balance + Movements methodology")
        logging.info("Glass materials: Start from 2024-11-01")
        logging.info("Others materials: Start from 2022-08-31")
        
        # Get the corrected SQL query
        corrected_query = get_corrected_stock_query()
        
        # Execute the query on Databricks
        raw_data_df = execute_databricks_query(corrected_query)
        
        # Process the data
        if not raw_data_df.empty:
            processed_df = process_corrected_data(raw_data_df)
            
            # Save the corrected output
            output_filename = f'PBI_Output_CORRECTED_Opening_Balance_Method.csv'
            processed_df.to_csv(output_filename, index=False)
            logging.info(f"Successfully created corrected output: {output_filename}")
            
            # Show summary statistics
            logging.info(f"Total records: {len(processed_df)}")
            if 'StockDifference' in processed_df.columns:
                total_diff = processed_df['StockDifference'].sum()
                logging.info(f"Total stock difference vs old method: {total_diff:,.0f} units")
                
                # Show sample of corrected vs old stock
                sample_comparison = processed_df[['SKU', 'Plant', 'Total Stock', 'OldMARDStock', 'StockDifference']].head(10)
                logging.info("Sample comparison (Corrected vs Old MARD):")
                logging.info(sample_comparison.to_string())
        else:
            logging.warning("No data returned from corrected query.")
            
    except Exception as e:
        logging.error(f"Error during corrected script execution: {e}")
