import pandas as pd
import logging
from databricks import sql

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_query(query: str, query_name: str, connection) -> pd.DataFrame:
    """Execute a SQL query and return results as DataFrame."""
    try:
        logging.info(f"Executing {query_name}...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        
        df = pd.DataFrame(data, columns=columns)
        logging.info(f"{query_name} completed. Fetched {len(df)} rows.")
        return df
    except Exception as e:
        logging.error(f"Error executing {query_name}: {e}")
        return pd.DataFrame()

def test_benchmark_sku_simple(connection):
    """Simple test of benchmark SKU 7547371"""
    logging.info("\n" + "="*80)
    logging.info("SIMPLE BENCHMARK TEST: SKU 7547371")
    logging.info("Benchmark: Opening=2443, Receipts=1595, Issues=3726, Closing=312")
    logging.info("="*80)
    
    # First, check if the SKU exists in our database
    query1 = """
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        SUM(labst) AS MARD_Stock
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
    WHERE matnr LIKE '%7547371%'
      AND werks = 'DE30'
    GROUP BY matnr, werks
    """
    
    result1 = execute_query(query1, "Check SKU Existence in MARD", connection)
    
    if not result1.empty:
        logging.info("Found SKU in MARD:")
        for _, row in result1.iterrows():
            logging.info(f"  SKU: {row['SKU']}, Plant: {row['Plant']}, MARD Stock: {float(row['MARD_Stock']):,.0f}")
        
        # Use the exact SKU format found
        exact_sku = result1.iloc[0]['SKU']
        
        # Now test movements
        query2 = f"""
        SELECT 
            SUM(CASE WHEN shkzg = 'S' THEN menge ELSE 0 END) AS Total_Receipts,
            SUM(CASE WHEN shkzg = 'H' THEN menge ELSE 0 END) AS Total_Issues,
            SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Net_Movements,
            COUNT(*) AS Movement_Count,
            MIN(cpudt_mkpf) AS Earliest_Date,
            MAX(cpudt_mkpf) AS Latest_Date
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        WHERE matnr = '{exact_sku}'
          AND werks = 'DE30'
        """
        
        result2 = execute_query(query2, "Check SKU Movements in MSEG", connection)
        
        if not result2.empty:
            row = result2.iloc[0]
            mseg_receipts = float(row['Total_Receipts']) if row['Total_Receipts'] is not None else 0
            mseg_issues = float(row['Total_Issues']) if row['Total_Issues'] is not None else 0
            mseg_net = float(row['Net_Movements']) if row['Net_Movements'] is not None else 0
            mard_stock = float(result1.iloc[0]['MARD_Stock'])
            
            # Benchmark values
            benchmark_opening = 2443
            benchmark_receipts = 1595
            benchmark_issues = 3726
            benchmark_closing = 312
            
            logging.info(f"\nCOMPARISON RESULTS:")
            logging.info(f"")
            logging.info(f"BENCHMARK DATA:")
            logging.info(f"  Opening Stock: {benchmark_opening:,}")
            logging.info(f"  Receipts: {benchmark_receipts:,}")
            logging.info(f"  Issues: {benchmark_issues:,}")
            logging.info(f"  Closing Stock: {benchmark_closing:,}")
            logging.info(f"  Formula Check: {benchmark_opening + benchmark_receipts - benchmark_issues:,}")
            
            logging.info(f"")
            logging.info(f"OUR DATABASE DATA:")
            logging.info(f"  MARD Current Stock: {mard_stock:,.0f}")
            logging.info(f"  MSEG Receipts: {mseg_receipts:,.0f}")
            logging.info(f"  MSEG Issues: {mseg_issues:,.0f}")
            logging.info(f"  MSEG Net Movements: {mseg_net:,.0f}")
            logging.info(f"  Movement Count: {int(row['Movement_Count']) if row['Movement_Count'] is not None else 0}")
            logging.info(f"  Date Range: {row['Earliest_Date']} to {row['Latest_Date']}")
            
            # Apply our methodology
            calculated_opening = benchmark_closing - mseg_net
            our_closing = calculated_opening + mseg_net
            
            logging.info(f"")
            logging.info(f"OUR METHODOLOGY:")
            logging.info(f"  Calculated Opening Inventory: {calculated_opening:,.0f}")
            logging.info(f"  Our Calculated Closing Stock: {our_closing:,.0f}")
            
            logging.info(f"")
            logging.info(f"DIFFERENCES:")
            logging.info(f"  Receipts (MSEG vs Benchmark): {mseg_receipts - benchmark_receipts:+,.0f}")
            logging.info(f"  Issues (MSEG vs Benchmark): {mseg_issues - benchmark_issues:+,.0f}")
            logging.info(f"  Opening (Calculated vs Benchmark): {calculated_opening - benchmark_opening:+,.0f}")
            logging.info(f"  Closing (Our Method vs Benchmark): {our_closing - benchmark_closing:+,.0f}")
            logging.info(f"  MARD vs Benchmark Closing: {mard_stock - benchmark_closing:+,.0f}")
            
            # Validation
            logging.info(f"")
            logging.info(f"🎯 VALIDATION:")
            if abs(our_closing - benchmark_closing) < 1:
                logging.info(f"  ✅ PERFECT MATCH! Our methodology works perfectly")
                return True
            elif abs(our_closing - benchmark_closing) < 10:
                logging.info(f"  ✅ VERY CLOSE! Within acceptable tolerance")
                return True
            else:
                logging.info(f"  ❌ MISMATCH: Difference of {our_closing - benchmark_closing:+,.0f}")
                
                # Check if the issue is with movement data differences
                if abs(mseg_receipts - benchmark_receipts) > 100 or abs(mseg_issues - benchmark_issues) > 100:
                    logging.info(f"  🔍 LIKELY CAUSE: Different movement data in MSEG vs benchmark")
                    logging.info(f"     The benchmark may use different date ranges or movement types")
                else:
                    logging.info(f"  🔍 LIKELY CAUSE: Different opening inventory calculation")
                
                return False
        else:
            logging.warning("No movement data found in MSEG for this SKU")
            return False
    else:
        logging.warning("SKU 7547371 not found in MARD table")
        
        # Try different SKU formats
        logging.info("Trying different SKU formats...")
        for sku_format in ['7547371', '07547371', '0007547371', '000007547371', '000000000007547371']:
            query_alt = f"""
            SELECT matnr AS SKU, werks AS Plant, SUM(labst) AS Stock
            FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
            WHERE matnr = '{sku_format}' AND werks = 'DE30'
            GROUP BY matnr, werks
            """
            result_alt = execute_query(query_alt, f"Try SKU format {sku_format}", connection)
            if not result_alt.empty:
                logging.info(f"Found SKU with format: {sku_format}")
                return test_with_exact_sku(connection, sku_format)
        
        return False

def test_with_exact_sku(connection, exact_sku):
    """Test with the exact SKU format found"""
    logging.info(f"Testing with exact SKU format: {exact_sku}")
    
    # Get MARD and MSEG data
    query = f"""
    WITH MARDData AS (
        SELECT SUM(labst) AS MARD_Stock
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
        WHERE matnr = '{exact_sku}' AND werks = 'DE30'
    ),
    MSEGData AS (
        SELECT 
            SUM(CASE WHEN shkzg = 'S' THEN menge ELSE 0 END) AS Receipts,
            SUM(CASE WHEN shkzg = 'H' THEN menge ELSE 0 END) AS Issues,
            SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Net_Movements
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        WHERE matnr = '{exact_sku}' AND werks = 'DE30'
    )
    SELECT 
        COALESCE(mard.MARD_Stock, 0) AS MARD_Stock,
        COALESCE(mseg.Receipts, 0) AS MSEG_Receipts,
        COALESCE(mseg.Issues, 0) AS MSEG_Issues,
        COALESCE(mseg.Net_Movements, 0) AS MSEG_Net_Movements,
        312.0 AS Benchmark_Closing,
        312.0 - COALESCE(mseg.Net_Movements, 0) AS Calculated_Opening,
        COALESCE(mseg.Net_Movements, 0) + (312.0 - COALESCE(mseg.Net_Movements, 0)) AS Our_Closing
    FROM MARDData mard
    CROSS JOIN MSEGData mseg
    """
    
    result = execute_query(query, f"Test Exact SKU {exact_sku}", connection)
    
    if not result.empty:
        row = result.iloc[0]
        our_closing = float(row['Our_Closing'])
        benchmark_closing = float(row['Benchmark_Closing'])
        
        logging.info(f"Results for {exact_sku}:")
        logging.info(f"  MARD Stock: {float(row['MARD_Stock']):,.0f}")
        logging.info(f"  MSEG Net Movements: {float(row['MSEG_Net_Movements']):,.0f}")
        logging.info(f"  Calculated Opening: {float(row['Calculated_Opening']):,.0f}")
        logging.info(f"  Our Closing Stock: {our_closing:,.0f}")
        logging.info(f"  Benchmark Closing: {benchmark_closing:,.0f}")
        logging.info(f"  Difference: {our_closing - benchmark_closing:+,.0f}")
        
        return abs(our_closing - benchmark_closing) < 1
    
    return False

def main():
    """Main function"""
    logging.info("=== SIMPLE BENCHMARK TEST FOR SKU 7547371 ===")
    
    try:
        connection = get_databricks_connection()
        success = test_benchmark_sku_simple(connection)
        
        logging.info("\n" + "="*80)
        logging.info("FINAL RESULT")
        logging.info("="*80)
        
        if success:
            logging.info("✅ SUCCESS: Our methodology works for the benchmark SKU!")
            logging.info("The opening inventory approach can be applied to other SKUs.")
        else:
            logging.info("❌ NEEDS REFINEMENT: The methodology requires adjustment.")
            logging.info("Consider investigating date ranges or movement type filters.")
        
        connection.close()
        
    except Exception as e:
        logging.error(f"Error during testing: {e}")

if __name__ == '__main__':
    main()
