import pandas as pd
import numpy as np
from datetime import date
import os
import logging
from databricks import sql

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """
    Establish and return a connection to the Databricks SQL warehouse.
    """
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        if not all([server_hostname, http_path, access_token]):
            raise ValueError("Missing one or more Databricks connection variables.")
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_databricks_query(query: str, connection=None) -> pd.DataFrame:
    """
    Execute a SQL query on Databricks and return results as a DataFrame.
    """
    close_conn = False
    if connection is None:
        connection = get_databricks_connection()
        close_conn = True
    try:
        logging.info("Executing query on Databricks...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        logging.info(f"Query successful. Fetched {len(data)} rows.")
        return pd.DataFrame(data, columns=columns)
    finally:
        if close_conn and connection:
            connection.close()
            logging.info("Databricks connection closed.")

def load_comparison_data():
    """
    Load the comparison data from the Excel file.
    """
    try:
        # Try to read the comparison file
        comparison_file = 'Comparision.xlsx'
        
        if os.path.exists(comparison_file):
            logging.info("Loading comparison data from Excel file...")
            
            # Try different sheet names
            try:
                df = pd.read_excel(comparison_file, sheet_name='Sheet2')
                logging.info("Successfully loaded data from 'Sheet2'")
            except:
                try:
                    df = pd.read_excel(comparison_file, sheet_name=1)  # Second sheet by index
                    logging.info("Successfully loaded data from second sheet")
                except:
                    df = pd.read_excel(comparison_file)  # Default sheet
                    logging.info("Successfully loaded data from default sheet")
            
            # Look for relevant columns
            sku_col = None
            plant_col = None
            target_stock_col = None
            
            for col in df.columns:
                col_lower = str(col).lower()
                if 'sku' in col_lower or 'material' in col_lower:
                    sku_col = col
                elif 'plant' in col_lower or 'werk' in col_lower:
                    plant_col = col
                elif 'target' in col_lower and 'stock' in col_lower:
                    target_stock_col = col
                elif 'stock' in col_lower and 'quantity' in col_lower:
                    target_stock_col = col
            
            if sku_col and plant_col and target_stock_col:
                result_df = df[[sku_col, plant_col, target_stock_col]].copy()
                result_df.columns = ['SKU', 'Plant', 'Target_Stock_Quantity']
                result_df.dropna(subset=['SKU', 'Plant', 'Target_Stock_Quantity'], inplace=True)
                logging.info(f"Successfully loaded {len(result_df)} comparison records")
                return result_df
            else:
                logging.warning(f"Could not find required columns. Available columns: {list(df.columns)}")
                return pd.DataFrame()
        else:
            logging.error(f"Comparison file not found: {comparison_file}")
            return pd.DataFrame()
            
    except Exception as e:
        logging.error(f"Error loading comparison data: {e}")
        return pd.DataFrame()

def test_hypothesis_1(sample_sku, sample_plant):
    """
    Hypothesis #1: The stock type is different.
    Test if LABST + INSME + SPEME + UMLME equals the Target_Stock_Quantity.
    """
    logging.info(f"\n=== TESTING HYPOTHESIS #1: Different Stock Types ===")
    logging.info(f"Sample SKU: {sample_sku}, Plant: {sample_plant}")
    
    query = f"""
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        lgort AS StorageLocation,
        labst AS Unrestricted_Stock,
        insme AS Quality_Inspection_Stock,
        speme AS Blocked_Stock,
        umlme AS Transfer_Stock,
        (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) AS Total_All_Stock_Types
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
    WHERE matnr = '{sample_sku}'
      AND werks = '{sample_plant}'
    """
    
    try:
        result_df = execute_databricks_query(query)
        if not result_df.empty:
            logging.info("MARD table results:")
            for _, row in result_df.iterrows():
                logging.info(f"  SKU: {row['SKU']}, Plant: {row['Plant']}")
                logging.info(f"  Unrestricted (LABST): {row['Unrestricted_Stock']}")
                logging.info(f"  Quality Inspection (INSME): {row['Quality_Inspection_Stock']}")
                logging.info(f"  Blocked (SPEME): {row['Blocked_Stock']}")
                logging.info(f"  Transfer (UMLME): {row['Transfer_Stock']}")
                logging.info(f"  TOTAL ALL TYPES: {row['Total_All_Stock_Types']}")
            return result_df
        else:
            logging.warning("No data found in MARD table for the sample SKU/Plant")
            return pd.DataFrame()
    except Exception as e:
        logging.error(f"Error executing Hypothesis 1 query: {e}")
        return pd.DataFrame()

def test_hypothesis_2(sample_sku, sample_plant):
    """
    Hypothesis #2: The stock is from a different point in time.
    Check MARDH table for historical stock values.
    """
    logging.info(f"\n=== TESTING HYPOTHESIS #2: Historical Stock Data ===")
    logging.info(f"Sample SKU: {sample_sku}, Plant: {sample_plant}")
    
    query = f"""
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        lgort AS StorageLocation,
        lfgja AS Fiscal_Year,
        lfmon AS Period,
        labst AS Historical_Unrestricted_Stock,
        insme AS Historical_Quality_Inspection_Stock,
        speme AS Historical_Blocked_Stock,
        umlme AS Historical_Transfer_Stock,
        (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) AS Historical_Total_All_Stock_Types
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mardh
    WHERE matnr = '{sample_sku}'
      AND werks = '{sample_plant}'
    ORDER BY lfgja DESC, lfmon DESC
    LIMIT 10
    """
    
    try:
        result_df = execute_databricks_query(query)
        if not result_df.empty:
            logging.info("MARDH table results (last 10 periods):")
            for _, row in result_df.iterrows():
                logging.info(f"  Period: {row['Fiscal_Year']}/{row['Period']:02d}")
                logging.info(f"  Historical Unrestricted: {row['Historical_Unrestricted_Stock']}")
                logging.info(f"  Historical Total All Types: {row['Historical_Total_All_Stock_Types']}")
            return result_df
        else:
            logging.warning("No historical data found in MARDH table for the sample SKU/Plant")
            return pd.DataFrame()
    except Exception as e:
        logging.error(f"Error executing Hypothesis 2 query: {e}")
        return pd.DataFrame()

def test_hypothesis_3(sample_sku, sample_plant):
    """
    Hypothesis #3: The aggregation logic is different.
    Calculate stock from MSEG movements.
    """
    logging.info(f"\n=== TESTING HYPOTHESIS #3: Movement-Based Stock Calculation ===")
    logging.info(f"Sample SKU: {sample_sku}, Plant: {sample_plant}")
    
    query = f"""
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Calculated_Stock_From_Movements,
        COUNT(*) AS Number_of_Movements,
        MIN(cpudt_mkpf) AS Earliest_Movement,
        MAX(cpudt_mkpf) AS Latest_Movement
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = '{sample_sku}'
      AND werks = '{sample_plant}'
    GROUP BY matnr, werks
    """
    
    try:
        result_df = execute_databricks_query(query)
        if not result_df.empty:
            logging.info("MSEG movement-based calculation results:")
            for _, row in result_df.iterrows():
                logging.info(f"  SKU: {row['SKU']}, Plant: {row['Plant']}")
                logging.info(f"  Calculated Stock from Movements: {row['Calculated_Stock_From_Movements']}")
                logging.info(f"  Number of Movements: {row['Number_of_Movements']}")
                logging.info(f"  Date Range: {row['Earliest_Movement']} to {row['Latest_Movement']}")
            return result_df
        else:
            logging.warning("No movement data found in MSEG table for the sample SKU/Plant")
            return pd.DataFrame()
    except Exception as e:
        logging.error(f"Error executing Hypothesis 3 query: {e}")
        return pd.DataFrame()

def main():
    """
    Main function to run the diagnostic tests.
    """
    logging.info("=== SAP STOCK RECONCILIATION DIAGNOSTIC ===")
    
    # Load comparison data
    comparison_df = load_comparison_data()
    
    if comparison_df.empty:
        logging.error("Could not load comparison data. Exiting.")
        return
    
    # Find a sample SKU with a mismatch (preferably SKU 7546978 at Plant DE30 as mentioned)
    sample_sku = "7546978"
    sample_plant = "DE30"
    
    # Check if this combination exists in our comparison data
    sample_row = comparison_df[(comparison_df['SKU'].astype(str) == sample_sku) & 
                              (comparison_df['Plant'] == sample_plant)]
    
    if sample_row.empty:
        # If the specific example doesn't exist, use the first row
        sample_row = comparison_df.iloc[0]
        sample_sku = str(sample_row['SKU'])
        sample_plant = sample_row['Plant']
        logging.info(f"Using first available sample: SKU {sample_sku}, Plant {sample_plant}")
    
    target_stock = sample_row['Target_Stock_Quantity'].iloc[0] if not sample_row.empty else comparison_df.iloc[0]['Target_Stock_Quantity']
    logging.info(f"Target Stock Quantity: {target_stock}")
    
    # Test each hypothesis
    h1_result = test_hypothesis_1(sample_sku, sample_plant)
    h2_result = test_hypothesis_2(sample_sku, sample_plant)
    h3_result = test_hypothesis_3(sample_sku, sample_plant)
    
    # Compare results with target
    logging.info(f"\n=== COMPARISON WITH TARGET STOCK ({target_stock}) ===")
    
    if not h1_result.empty:
        total_all_types = h1_result['Total_All_Stock_Types'].iloc[0]
        logging.info(f"Hypothesis 1 - Total All Stock Types: {total_all_types}")
        if abs(total_all_types - target_stock) < 0.01:  # Allow for small floating point differences
            logging.info("✅ HYPOTHESIS 1 MATCHES! The issue is different stock types.")
        else:
            logging.info(f"❌ Hypothesis 1 doesn't match. Difference: {total_all_types - target_stock}")
    
    if not h2_result.empty:
        for _, row in h2_result.iterrows():
            hist_total = row['Historical_Total_All_Stock_Types']
            period = f"{row['Fiscal_Year']}/{row['Period']:02d}"
            logging.info(f"Hypothesis 2 - Period {period}: {hist_total}")
            if abs(hist_total - target_stock) < 0.01:
                logging.info(f"✅ HYPOTHESIS 2 MATCHES for period {period}! The issue is historical timing.")
                break
        else:
            logging.info("❌ Hypothesis 2 doesn't match any historical periods.")
    
    if not h3_result.empty:
        calc_stock = h3_result['Calculated_Stock_From_Movements'].iloc[0]
        logging.info(f"Hypothesis 3 - Movement-based Stock: {calc_stock}")
        if abs(calc_stock - target_stock) < 0.01:
            logging.info("✅ HYPOTHESIS 3 MATCHES! The issue is aggregation logic.")
        else:
            logging.info(f"❌ Hypothesis 3 doesn't match. Difference: {calc_stock - target_stock}")

if __name__ == '__main__':
    main()
