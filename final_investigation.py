import pandas as pd
import logging
from databricks import sql

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_query(query: str, query_name: str, connection) -> pd.DataFrame:
    """Execute a SQL query and return results as DataFrame."""
    try:
        logging.info(f"Executing {query_name}...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        
        df = pd.DataFrame(data, columns=columns)
        logging.info(f"{query_name} completed. Fetched {len(df)} rows.")
        return df
    except Exception as e:
        logging.info(f"Error executing {query_name}: {e}")
        return pd.DataFrame()

def investigate_mard_mseg_discrepancy(connection):
    """Investigate why MARD (22,758) differs from MSEG movements (16,962)"""
    logging.info("\n" + "="*80)
    logging.info("INVESTIGATING MARD vs MSEG DISCREPANCY")
    logging.info("MARD shows 22,758 but MSEG movements only sum to 16,962")
    logging.info("Missing: 5,796 units in movement history")
    logging.info("="*80)
    
    # Check if there are movements before our earliest date
    query = """
    SELECT 
        MIN(cpudt_mkpf) AS Earliest_Movement,
        MAX(cpudt_mkpf) AS Latest_Movement,
        COUNT(*) AS Total_Movements,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Total_Net_Movement,
        SUM(CASE WHEN cpudt_mkpf < '2020-01-01' THEN (CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) ELSE 0 END) AS Pre_2020_Movement,
        SUM(CASE WHEN cpudt_mkpf >= '2020-01-01' THEN (CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) ELSE 0 END) AS Post_2020_Movement
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    """
    
    result = execute_query(query, "Movement Date Analysis", connection)
    
    if not result.empty:
        row = result.iloc[0]
        logging.info(f"Movement history analysis:")
        logging.info(f"  Date range: {row['Earliest_Movement']} to {row['Latest_Movement']}")
        logging.info(f"  Total movements: {int(row['Total_Movements']):,}")
        logging.info(f"  Total net movement: {float(row['Total_Net_Movement']):,.0f}")
        logging.info(f"  Pre-2020 movement: {float(row['Pre_2020_Movement']):,.0f}")
        logging.info(f"  Post-2020 movement: {float(row['Post_2020_Movement']):,.0f}")
        
        # The discrepancy suggests there might be an opening balance
        opening_balance = 22758 - float(row['Total_Net_Movement'])
        logging.info(f"  Implied opening balance: {opening_balance:,.0f}")

def test_consignment_stock(connection):
    """Test consignment stock (MSLB table)"""
    logging.info("\n" + "="*80)
    logging.info("TESTING CONSIGNMENT STOCK (MSLB)")
    logging.info("="*80)
    
    query = """
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        lgort AS Storage_Location,
        lblab AS Consignment_Stock,
        SUM(lblab) AS Total_Consignment_Stock,
        23070.0 AS Target_Stock,
        SUM(lblab) - 23070.0 AS Difference_From_Target
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mslb
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    GROUP BY matnr, werks, lgort, lblab
    """
    
    result = execute_query(query, "Consignment Stock", connection)
    
    if not result.empty:
        total_consignment = float(result['Total_Consignment_Stock'].sum())
        logging.info(f"Consignment stock found: {total_consignment:,.0f} units")
        
        # Check if MARD + Consignment = Target
        combined_with_consignment = 22758 + total_consignment
        diff_combined = combined_with_consignment - 23070
        logging.info(f"MARD + Consignment: {combined_with_consignment:,.0f} units")
        logging.info(f"Difference from target: {diff_combined:+,.0f}")
        
        if abs(diff_combined) < 10:
            logging.info("✅ SOLUTION FOUND! MARD + Consignment stock = Target")
            return True
    else:
        logging.info("❌ No consignment stock found")
    
    return False

def test_material_variants(connection):
    """Test if there are material number variants"""
    logging.info("\n" + "="*80)
    logging.info("TESTING MATERIAL NUMBER VARIANTS")
    logging.info("="*80)
    
    # Look for similar material numbers
    query = """
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        SUM(labst) AS Stock,
        COUNT(*) AS Location_Count
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
    WHERE werks = 'DE30'
      AND (matnr LIKE '%7546978%' OR matnr LIKE '000000000007546978%')
      AND labst > 0
    GROUP BY matnr, werks
    ORDER BY Stock DESC
    """
    
    result = execute_query(query, "Material Variants", connection)
    
    if not result.empty:
        logging.info("Material variants found:")
        total_variant_stock = 0
        for _, row in result.iterrows():
            stock = float(row['Stock'])
            total_variant_stock += stock
            logging.info(f"  {row['SKU']}: {stock:,.0f} units")
        
        logging.info(f"Total across all variants: {total_variant_stock:,.0f} units")
        diff = total_variant_stock - 23070
        logging.info(f"Difference from target: {diff:+,.0f}")
        
        if abs(diff) < 10:
            logging.info("✅ SOLUTION FOUND! Multiple material variants sum to target")
            return True
    else:
        logging.info("❌ No material variants found")
    
    return False

def test_cross_plant_analysis(connection):
    """Test if stock from multiple plants should be combined"""
    logging.info("\n" + "="*80)
    logging.info("TESTING CROSS-PLANT STOCK COMBINATION")
    logging.info("="*80)
    
    query = """
    SELECT 
        werks AS Plant,
        SUM(labst) AS Plant_Stock,
        COUNT(*) AS Location_Count
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
    WHERE matnr = '000000000007546978'
      AND labst > 0
    GROUP BY werks
    ORDER BY Plant_Stock DESC
    """
    
    result = execute_query(query, "Cross-Plant Analysis", connection)
    
    if not result.empty:
        logging.info("Stock by plant:")
        total_all_plants = 0
        for _, row in result.iterrows():
            stock = float(row['Plant_Stock'])
            total_all_plants += stock
            logging.info(f"  Plant {row['Plant']}: {stock:,.0f} units")
        
        logging.info(f"Total across all plants: {total_all_plants:,.0f} units")
        diff = total_all_plants - 23070
        logging.info(f"Difference from target: {diff:+,.0f}")
        
        if abs(diff) < 10:
            logging.info("✅ SOLUTION FOUND! Cross-plant total matches target")
            return True
    else:
        logging.info("❌ No cross-plant data found")
    
    return False

def test_opening_balance_theory(connection):
    """Test the theory that target includes an opening balance"""
    logging.info("\n" + "="*80)
    logging.info("TESTING OPENING BALANCE THEORY")
    logging.info("Target (23,070) = Opening Balance + Net Movements")
    logging.info("="*80)
    
    # Calculate what the opening balance would need to be
    mseg_total = 16962  # From previous analysis
    mard_current = 22758
    target = 23070
    
    implied_opening_from_target = target - mseg_total
    implied_opening_from_mard = mard_current - mseg_total
    
    logging.info(f"Analysis:")
    logging.info(f"  Current MARD stock: {mard_current:,} units")
    logging.info(f"  MSEG net movements: {mseg_total:,} units")
    logging.info(f"  Target stock: {target:,} units")
    logging.info(f"")
    logging.info(f"  Implied opening balance (from target): {implied_opening_from_target:,} units")
    logging.info(f"  Implied opening balance (from MARD): {implied_opening_from_mard:,} units")
    logging.info(f"")
    logging.info(f"  If target = opening + movements: {implied_opening_from_target + mseg_total:,} = {target:,} ✅")
    logging.info(f"  If MARD = opening + movements: {implied_opening_from_mard + mseg_total:,} = {mard_current:,} ✅")
    
    # The difference between the two opening balances
    opening_diff = implied_opening_from_target - implied_opening_from_mard
    logging.info(f"")
    logging.info(f"  Difference in opening balances: {opening_diff:+,} units")
    logging.info(f"  This matches our target difference: {target - mard_current:+,} units ✅")
    
    return True

def main():
    """Main final investigation function"""
    logging.info("=== FINAL INVESTIGATION: THE MISSING 312 UNITS ===")
    logging.info("Current findings:")
    logging.info("  - MARD stock: 22,758 units")
    logging.info("  - MSEG movements (to July 31): 16,962 units")
    logging.info("  - Target: 23,070 units")
    logging.info("  - MARD vs Target: -312 units")
    logging.info("  - MARD vs MSEG: +5,796 units")
    
    try:
        connection = get_databricks_connection()
        
        # Run all final investigations
        investigate_mard_mseg_discrepancy(connection)
        consignment_match = test_consignment_stock(connection)
        variant_match = test_material_variants(connection)
        cross_plant_match = test_cross_plant_analysis(connection)
        opening_balance_analysis = test_opening_balance_theory(connection)
        
        # Final conclusion
        logging.info("\n" + "="*80)
        logging.info("FINAL INVESTIGATION RESULTS")
        logging.info("="*80)
        
        if consignment_match:
            logging.info("🎯 SOLUTION: Include consignment stock (MSLB) in calculation")
        elif variant_match:
            logging.info("🎯 SOLUTION: Sum multiple material number variants")
        elif cross_plant_match:
            logging.info("🎯 SOLUTION: Combine stock across multiple plants")
        else:
            logging.info("🎯 MOST LIKELY SOLUTION: Opening Balance Discrepancy")
            logging.info("   The target (23,070) appears to include a different opening")
            logging.info("   balance than what's reflected in the current MARD table.")
            logging.info("   ")
            logging.info("   RECOMMENDED ACTION:")
            logging.info("   1. Verify the source of the target figure (23,070)")
            logging.info("   2. Check if it includes historical opening balances")
            logging.info("   3. Consider using a different base date for stock calculation")
            logging.info("   4. Investigate if manual adjustments were made to the target")
        
        connection.close()
        
    except Exception as e:
        logging.error(f"Error during final investigation: {e}")

if __name__ == '__main__':
    main()
