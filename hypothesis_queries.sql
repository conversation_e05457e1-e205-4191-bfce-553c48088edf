-- SAP Stock Reconciliation Diagnostic Queries
-- Based on the comparison data analysis, we need to test why our current script
-- produces 22,758 units while the target should be 23,070 units for SKU 7546978 at Plant DE30
-- Difference: 312 units short

-- =============================================================================
-- HYPOTHESIS #1: The stock type is different
-- Current query uses LABST (unrestricted stock) only
-- Target might include other stock types: INSME, SPEME, UMLME
-- =============================================================================

-- Query H1: Check all stock types in MARD for sample SKU
SELECT 
    matnr AS SKU,
    werks AS Plant,
    lgort AS StorageLocation,
    labst AS Unrestricted_Stock_LABST,
    insme AS Quality_Inspection_Stock_INSME,
    speme AS Blocked_Stock_SPEME,
    umlme AS Transfer_Stock_UMLME,
    (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) AS Total_All_Stock_Types,
    -- Compare with target
    23070.0 AS Target_Stock,
    (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) - 23070.0 AS Difference_From_Target
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
WHERE matnr = '7546978'
  AND werks = 'DE30'
ORDER BY lgort;

-- =============================================================================
-- HYPOTHESIS #2: The stock is from a different point in time
-- Current query uses MARD (current stock)
-- Target might be from MARDH (historical stock at period end)
-- =============================================================================

-- Query H2: Check historical stock values in MARDH for sample SKU
SELECT 
    matnr AS SKU,
    werks AS Plant,
    lgort AS StorageLocation,
    lfgja AS Fiscal_Year,
    lfmon AS Period,
    CONCAT(lfgja, '-', LPAD(lfmon, 2, '0')) AS Period_Label,
    labst AS Historical_Unrestricted_Stock,
    insme AS Historical_Quality_Inspection_Stock,
    speme AS Historical_Blocked_Stock,
    umlme AS Historical_Transfer_Stock,
    (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) AS Historical_Total_All_Stock_Types,
    -- Compare with target
    23070.0 AS Target_Stock,
    (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) - 23070.0 AS Difference_From_Target
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mardh
WHERE matnr = '7546978'
  AND werks = 'DE30'
ORDER BY lfgja DESC, lfmon DESC
LIMIT 12; -- Last 12 periods

-- =============================================================================
-- HYPOTHESIS #3: The aggregation logic is different
-- Current query joins MSEG movements with MARD stock
-- Target might be calculated differently from MSEG movements
-- =============================================================================

-- Query H3A: Check movement-based stock calculation from MSEG
SELECT 
    matnr AS SKU,
    werks AS Plant,
    SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Calculated_Stock_From_Movements,
    COUNT(*) AS Number_of_Movements,
    MIN(cpudt_mkpf) AS Earliest_Movement,
    MAX(cpudt_mkpf) AS Latest_Movement,
    -- Compare with target
    23070.0 AS Target_Stock,
    SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) - 23070.0 AS Difference_From_Target
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
WHERE matnr = '7546978'
  AND werks = 'DE30'
GROUP BY matnr, werks;

-- Query H3B: Check if there are movements after a certain date that might be excluded
SELECT 
    matnr AS SKU,
    werks AS Plant,
    cpudt_mkpf AS Movement_Date,
    bwart AS Movement_Type,
    shkzg AS Debit_Credit_Indicator,
    menge AS Quantity,
    CASE WHEN shkzg = 'S' THEN menge ELSE -menge END AS Net_Quantity,
    lgort AS Storage_Location
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
WHERE matnr = '7546978'
  AND werks = 'DE30'
  AND cpudt_mkpf >= '2025-07-01'  -- Recent movements
ORDER BY cpudt_mkpf DESC, mblnr DESC
LIMIT 20;

-- =============================================================================
-- ADDITIONAL DIAGNOSTIC QUERIES
-- =============================================================================

-- Query AD1: Check our current script logic for this specific SKU
-- This replicates the exact logic from the current script
WITH StockMovements AS (
    SELECT
        matnr AS SKU,
        werks AS Plant,
        lgort AS StorageLocation,
        bwart AS MovementType,
        cpudt_mkpf AS EntryDate,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS NetQuantity
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE werks = 'DE30'
      AND matnr = '7546978'
    GROUP BY SKU, Plant, StorageLocation, MovementType, EntryDate
),
CurrentStockAndValue AS (
    SELECT
        mard.matnr AS SKU,
        mard.werks AS Plant,
        makt.maktx AS MaterialDescription,
        mbew.verpr AS MovingPrice,
        mbew.peinh AS PriceUnit,
        mard.labst AS TotalStock,
        (mard.labst * (mbew.verpr / NULLIF(mbew.peinh, 0))) AS TotalStockValue
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard AS mard
    LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS mbew
        ON mard.matnr = mbew.matnr AND mard.werks = mbew.bwkey
    LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS makt
        ON mard.matnr = makt.matnr AND makt.spras = 'E'
    WHERE mard.werks = 'DE30'
      AND mard.matnr = '7546978'
)
SELECT
    'Current Script Logic' AS Query_Type,
    cs.SKU,
    cs.Plant,
    cs.TotalStock AS Current_Script_Stock,
    23070.0 AS Target_Stock,
    cs.TotalStock - 23070.0 AS Difference_From_Target,
    COUNT(mov.NetQuantity) AS Number_of_Movement_Records
FROM CurrentStockAndValue cs
LEFT JOIN StockMovements mov ON TRIM(LEADING '0' FROM mov.SKU) = TRIM(LEADING '0' FROM cs.SKU) 
    AND mov.Plant = cs.Plant
GROUP BY cs.SKU, cs.Plant, cs.TotalStock;

-- Query AD2: Check if there are multiple storage locations that need to be aggregated
SELECT 
    matnr AS SKU,
    werks AS Plant,
    lgort AS Storage_Location,
    labst AS Stock_Per_Location,
    SUM(labst) OVER (PARTITION BY matnr, werks) AS Total_Stock_All_Locations,
    23070.0 AS Target_Stock,
    SUM(labst) OVER (PARTITION BY matnr, werks) - 23070.0 AS Difference_From_Target
FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
WHERE matnr = '7546978'
  AND werks = 'DE30'
ORDER BY lgort;
