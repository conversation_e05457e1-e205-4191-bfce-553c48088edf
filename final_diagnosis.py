import pandas as pd
import logging
from databricks import sql

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_query(query: str, query_name: str, connection) -> pd.DataFrame:
    """Execute a SQL query and return results as DataFrame."""
    try:
        logging.info(f"Executing {query_name}...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        
        df = pd.DataFrame(data, columns=columns)
        logging.info(f"{query_name} completed. Fetched {len(df)} rows.")
        return df
    except Exception as e:
        logging.error(f"Error executing {query_name}: {e}")
        return pd.DataFrame()

def test_hypothesis_2_simple(connection):
    """Test Hypothesis 2: Historical timing - simplified version"""
    logging.info("\n" + "="*80)
    logging.info("HYPOTHESIS #2: Historical Stock Data (MARDH)")
    logging.info("="*80)
    
    query = """
    SELECT 
        lfgja AS Fiscal_Year,
        lfmon AS Period,
        SUM(labst) AS Historical_Stock,
        23070.0 AS Target_Stock,
        SUM(labst) - 23070.0 AS Difference_From_Target
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mardh
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    GROUP BY lfgja, lfmon
    ORDER BY lfgja DESC, lfmon DESC
    LIMIT 6
    """
    
    result = execute_query(query, "Hypothesis 2 - Historical", connection)
    
    if not result.empty:
        logging.info("Historical Stock Analysis:")
        for _, row in result.iterrows():
            period = f"{row['Fiscal_Year']}-{row['Period']:02d}"
            hist_stock = float(row['Historical_Stock'])
            diff = float(row['Difference_From_Target'])
            
            logging.info(f"  Period {period}: {hist_stock:,.0f} units (diff: {diff:+,.0f})")
            
            if abs(diff) < 10.0:  # Allow small tolerance
                logging.info(f"    ✅ CLOSE MATCH in period {period}!")
                return True
        
        logging.info("❌ No close matches in historical data")
    else:
        logging.info("❌ No historical data found")
    
    return False

def test_hypothesis_3_simple(connection):
    """Test Hypothesis 3: Movement calculation - simplified"""
    logging.info("\n" + "="*80)
    logging.info("HYPOTHESIS #3: Movement-Based Calculation (MSEG)")
    logging.info("="*80)
    
    query = """
    SELECT 
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Movement_Based_Stock,
        COUNT(*) AS Movement_Count,
        23070.0 AS Target_Stock,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) - 23070.0 AS Difference_From_Target
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    """
    
    result = execute_query(query, "Hypothesis 3 - Movements", connection)
    
    if not result.empty:
        row = result.iloc[0]
        movement_stock = float(row['Movement_Based_Stock'])
        diff = float(row['Difference_From_Target'])
        count = int(row['Movement_Count'])
        
        logging.info(f"Movement-Based Stock: {movement_stock:,.0f} units")
        logging.info(f"Number of Movements: {count:,}")
        logging.info(f"Difference from Target: {diff:+,.0f}")
        
        if abs(diff) < 10.0:
            logging.info("✅ CLOSE MATCH with movement-based calculation!")
            return True
        else:
            logging.info("❌ Movement-based calculation doesn't match")
    else:
        logging.info("❌ No movement data found")
    
    return False

def investigate_missing_312_units(connection):
    """Investigate what could account for the missing 312 units"""
    logging.info("\n" + "="*80)
    logging.info("INVESTIGATING THE MISSING 312 UNITS")
    logging.info("="*80)
    
    # Check if there are recent movements that might not be reflected in MARD
    query = """
    SELECT 
        cpudt_mkpf AS Movement_Date,
        bwart AS Movement_Type,
        shkzg AS Debit_Credit,
        menge AS Quantity,
        CASE WHEN shkzg = 'S' THEN menge ELSE -menge END AS Net_Quantity,
        lgort AS Storage_Location
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
      AND cpudt_mkpf >= '2025-07-01'
    ORDER BY cpudt_mkpf DESC
    LIMIT 10
    """
    
    result = execute_query(query, "Recent Movements", connection)
    
    if not result.empty:
        logging.info("Recent movements (since July 2025):")
        total_recent = 0
        for _, row in result.iterrows():
            net_qty = float(row['Net_Quantity'])
            total_recent += net_qty
            logging.info(f"  {row['Movement_Date']}: {row['Movement_Type']} {net_qty:+,.0f} units (Storage: {row['Storage_Location']})")
        
        logging.info(f"Total recent net movement: {total_recent:+,.0f} units")
        
        if abs(total_recent - 312) < 10:
            logging.info("✅ Recent movements might explain the difference!")
            return True
    else:
        logging.info("No recent movements found")
    
    return False

def main():
    """Main diagnostic function"""
    logging.info("=== FINAL SAP STOCK RECONCILIATION DIAGNOSIS ===")
    logging.info("SKU: 000000000007546978 at Plant DE30")
    logging.info("Current Script: 22,758 units")
    logging.info("Target (Ground Truth): 23,070 units")
    logging.info("Missing: 312 units")
    logging.info("")
    logging.info("HYPOTHESIS 1 RESULT: ❌ All stock types still = 22,758 (not the solution)")
    
    try:
        connection = get_databricks_connection()
        
        # Test remaining hypotheses
        h2_match = test_hypothesis_2_simple(connection)
        h3_match = test_hypothesis_3_simple(connection)
        
        # Additional investigation
        recent_movements = investigate_missing_312_units(connection)
        
        # Final summary
        logging.info("\n" + "="*80)
        logging.info("FINAL DIAGNOSIS SUMMARY")
        logging.info("="*80)
        logging.info("Hypothesis 1 (Stock Types): ❌ NO - All types = 22,758")
        logging.info(f"Hypothesis 2 (Historical): {'✅ YES' if h2_match else '❌ NO'}")
        logging.info(f"Hypothesis 3 (Movements): {'✅ YES' if h3_match else '❌ NO'}")
        logging.info(f"Recent Movement Analysis: {'✅ EXPLAINS DIFFERENCE' if recent_movements else '❌ NO CLEAR PATTERN'}")
        
        if h2_match:
            logging.info("\n🎯 SOLUTION: Use historical stock data from MARDH table")
            logging.info("   Modify query to use MARDH instead of MARD for a specific period")
        elif h3_match:
            logging.info("\n🎯 SOLUTION: Use movement-based calculation from MSEG")
            logging.info("   Calculate stock as SUM(CASE WHEN shkzg='S' THEN menge ELSE -menge END)")
        elif recent_movements:
            logging.info("\n🎯 SOLUTION: Account for recent movements or timing differences")
            logging.info("   The 312 unit difference may be due to recent transactions")
        else:
            logging.info("\n❓ FURTHER INVESTIGATION NEEDED")
            logging.info("   Consider checking:")
            logging.info("   - Different plant/storage location combinations")
            logging.info("   - Batch/lot-specific stock")
            logging.info("   - Special stock categories")
        
        connection.close()
        
    except Exception as e:
        logging.error(f"Error during diagnosis: {e}")

if __name__ == '__main__':
    main()
