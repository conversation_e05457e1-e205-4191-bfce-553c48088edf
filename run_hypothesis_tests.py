import pandas as pd
import logging
from databricks import sql
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_query(query: str, query_name: str, connection) -> pd.DataFrame:
    """Execute a SQL query and return results as DataFrame."""
    try:
        logging.info(f"Executing {query_name}...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        
        df = pd.DataFrame(data, columns=columns)
        logging.info(f"{query_name} completed. Fetched {len(df)} rows.")
        return df
    except Exception as e:
        logging.error(f"Error executing {query_name}: {e}")
        return pd.DataFrame()

def test_hypothesis_1(connection):
    """Test Hypothesis 1: Different stock types"""
    logging.info("\n" + "="*80)
    logging.info("HYPOTHESIS #1: The stock type is different")
    logging.info("Testing if LABST + INSME + SPEME + UMLME equals target (23,070)")
    logging.info("="*80)
    
    query = """
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        lgort AS StorageLocation,
        labst AS Unrestricted_Stock_LABST,
        insme AS Quality_Inspection_Stock_INSME,
        speme AS Blocked_Stock_SPEME,
        umlme AS Transfer_Stock_UMLME,
        (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) AS Total_All_Stock_Types,
        23070.0 AS Target_Stock,
        (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) - 23070.0 AS Difference_From_Target
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    ORDER BY lgort
    """
    
    result = execute_query(query, "Hypothesis 1", connection)
    
    if not result.empty:
        logging.info("MARD Stock Types Analysis:")
        for _, row in result.iterrows():
            logging.info(f"  Storage Location: {row['StorageLocation']}")
            logging.info(f"  Unrestricted (LABST): {row['Unrestricted_Stock_LABST']}")
            logging.info(f"  Quality Inspection (INSME): {row['Quality_Inspection_Stock_INSME']}")
            logging.info(f"  Blocked (SPEME): {row['Blocked_Stock_SPEME']}")
            logging.info(f"  Transfer (UMLME): {row['Transfer_Stock_UMLME']}")
            logging.info(f"  TOTAL ALL TYPES: {row['Total_All_Stock_Types']}")
            logging.info(f"  Difference from Target: {row['Difference_From_Target']}")
            
            if abs(row['Difference_From_Target']) < 1.0:
                logging.info("  ✅ HYPOTHESIS 1 MATCHES! The issue is different stock types.")
                return True
            logging.info("")
        
        # Check if sum across all storage locations matches
        total_all_types = float(result['Total_All_Stock_Types'].sum())
        diff_from_target = total_all_types - 23070.0
        logging.info(f"SUMMARY - Total across all storage locations: {total_all_types}")
        logging.info(f"SUMMARY - Difference from target: {diff_from_target}")
        
        if abs(diff_from_target) < 1.0:
            logging.info("✅ HYPOTHESIS 1 MATCHES! (Sum across storage locations)")
            return True
        else:
            logging.info("❌ Hypothesis 1 doesn't match.")
    else:
        logging.warning("No data found for Hypothesis 1")
    
    return False

def test_hypothesis_2(connection):
    """Test Hypothesis 2: Historical timing"""
    logging.info("\n" + "="*80)
    logging.info("HYPOTHESIS #2: The stock is from a different point in time")
    logging.info("Testing historical stock values from MARDH")
    logging.info("="*80)
    
    query = """
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        lgort AS StorageLocation,
        lfgja AS Fiscal_Year,
        lfmon AS Period,
        CONCAT(lfgja, '-', LPAD(lfmon, 2, '0')) AS Period_Label,
        labst AS Historical_Unrestricted_Stock,
        (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) AS Historical_Total_All_Stock_Types,
        23070.0 AS Target_Stock,
        (labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) - 23070.0 AS Difference_From_Target
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mardh
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    ORDER BY lfgja DESC, lfmon DESC
    LIMIT 12
    """
    
    result = execute_query(query, "Hypothesis 2", connection)
    
    if not result.empty:
        logging.info("Historical Stock Analysis (MARDH):")
        matches_found = []
        
        for _, row in result.iterrows():
            period = row['Period_Label']
            hist_stock = row['Historical_Total_All_Stock_Types']
            diff = row['Difference_From_Target']
            
            logging.info(f"  Period {period}: {hist_stock} (diff: {diff})")
            
            if abs(diff) < 1.0:
                matches_found.append(period)
                logging.info(f"    ✅ MATCH FOUND for period {period}!")
        
        if matches_found:
            logging.info(f"✅ HYPOTHESIS 2 MATCHES! Found matches in periods: {matches_found}")
            return True
        else:
            logging.info("❌ Hypothesis 2 doesn't match any historical periods.")
    else:
        logging.warning("No historical data found for Hypothesis 2")
    
    return False

def test_hypothesis_3(connection):
    """Test Hypothesis 3: Movement-based calculation"""
    logging.info("\n" + "="*80)
    logging.info("HYPOTHESIS #3: The aggregation logic is different")
    logging.info("Testing movement-based stock calculation from MSEG")
    logging.info("="*80)
    
    query = """
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Calculated_Stock_From_Movements,
        COUNT(*) AS Number_of_Movements,
        MIN(cpudt_mkpf) AS Earliest_Movement,
        MAX(cpudt_mkpf) AS Latest_Movement,
        23070.0 AS Target_Stock,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) - 23070.0 AS Difference_From_Target
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    GROUP BY matnr, werks
    """
    
    result = execute_query(query, "Hypothesis 3", connection)
    
    if not result.empty:
        row = result.iloc[0]
        calc_stock = row['Calculated_Stock_From_Movements']
        diff = row['Difference_From_Target']
        
        logging.info("Movement-Based Stock Calculation (MSEG):")
        logging.info(f"  Calculated Stock: {calc_stock}")
        logging.info(f"  Number of Movements: {row['Number_of_Movements']}")
        logging.info(f"  Date Range: {row['Earliest_Movement']} to {row['Latest_Movement']}")
        logging.info(f"  Difference from Target: {diff}")
        
        if abs(diff) < 1.0:
            logging.info("✅ HYPOTHESIS 3 MATCHES! The issue is aggregation logic.")
            return True
        else:
            logging.info("❌ Hypothesis 3 doesn't match.")
    else:
        logging.warning("No movement data found for Hypothesis 3")
    
    return False

def main():
    """Main function to run all hypothesis tests"""
    logging.info("=== SAP STOCK RECONCILIATION DIAGNOSTIC ===")
    logging.info("Target: SKU 000000000007546978 at Plant DE30 should have 23,070 units")
    logging.info("Current: Our script shows 22,758 units (312 units short)")
    
    try:
        connection = get_databricks_connection()
        
        # Test each hypothesis
        h1_match = test_hypothesis_1(connection)
        h2_match = test_hypothesis_2(connection)
        h3_match = test_hypothesis_3(connection)
        
        # Summary
        logging.info("\n" + "="*80)
        logging.info("SUMMARY OF RESULTS")
        logging.info("="*80)
        logging.info(f"Hypothesis 1 (Stock Types): {'✅ MATCH' if h1_match else '❌ NO MATCH'}")
        logging.info(f"Hypothesis 2 (Historical Timing): {'✅ MATCH' if h2_match else '❌ NO MATCH'}")
        logging.info(f"Hypothesis 3 (Movement Logic): {'✅ MATCH' if h3_match else '❌ NO MATCH'}")
        
        if h1_match:
            logging.info("\n🎯 SOLUTION FOUND: Use all stock types (LABST + INSME + SPEME + UMLME)")
        elif h2_match:
            logging.info("\n🎯 SOLUTION FOUND: Use historical stock data from MARDH")
        elif h3_match:
            logging.info("\n🎯 SOLUTION FOUND: Use movement-based calculation from MSEG")
        else:
            logging.info("\n❓ No hypothesis matched. Further investigation needed.")
        
        connection.close()
        
    except Exception as e:
        logging.error(f"Error during execution: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
