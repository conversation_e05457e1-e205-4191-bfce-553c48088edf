-- ============================================================================
-- CORRECTED SAP STOCK CALCULATION QUERY
-- Methodology: Opening Balance + Receipts - Issues = Closing Stock
-- Different date ranges based on Material Category (Glass vs Others)
-- ============================================================================

WITH MaterialCategories AS (
    -- Define material categories based on the Excel mapping
    -- Glass: Materials from the "Glass and others" sheet marked as "Glass"
    -- Others: All other materials
    SELECT 
        matnr AS Material,
        CASE 
            WHEN matnr IN (
                -- Glass materials (sample from the Excel file)
                -- In production, this should be loaded from the Excel file or a lookup table
                7546978, 7546979, 7546980, 7546988, 7546989, 7547015, 7547111, 7547112,
                7547600, 7547601, 7547602, 7547603, 7547604, 7547605, 7547606, 7547607,
                7547608, 7547609, 7547610, 7547611, 7547612, 7547613, 7547614, 7547615,
                7547616, 7547617, 7547618, 7547619, 7547620, 7547621, 7547622, 7547623,
                7547624, 7547625, 7547626, 7547627, 7547628, 7547629, 7547630, 7547631,
                7547632, 7547633, 7547634, 7547635, 7547636, 7547637, 7547638, 7547639,
                7547640, 7547641, 7547642, 7547643, 7547644, 7547645, 7547646, 7547647,
                7547648, 7547649, 7547650, 7547651, 7547652, 7547653, 7547654, 7547655,
                7547656, 7547657, 7547658, 7547659, 7547660, 7547661, 7547662, 7547663,
                7547664, 7547665, 7547666, 7547667, 7547668, 7547669, 7547670
                -- Add more Glass materials as needed
            ) THEN 'Glass'
            ELSE 'Others'
        END AS Category,
        CASE 
            WHEN matnr IN (
                -- Same Glass materials list
                7546978, 7546979, 7546980, 7546988, 7546989, 7547015, 7547111, 7547112,
                7547600, 7547601, 7547602, 7547603, 7547604, 7547605, 7547606, 7547607,
                7547608, 7547609, 7547610, 7547611, 7547612, 7547613, 7547614, 7547615,
                7547616, 7547617, 7547618, 7547619, 7547620, 7547621, 7547622, 7547623,
                7547624, 7547625, 7547626, 7547627, 7547628, 7547629, 7547630, 7547631,
                7547632, 7547633, 7547634, 7547635, 7547636, 7547637, 7547638, 7547639,
                7547640, 7547641, 7547642, 7547643, 7547644, 7547645, 7547646, 7547647,
                7547648, 7547649, 7547650, 7547651, 7547652, 7547653, 7547654, 7547655,
                7547656, 7547657, 7547658, 7547659, 7547660, 7547661, 7547662, 7547663,
                7547664, 7547665, 7547666, 7547667, 7547668, 7547669, 7547670
            ) THEN '2024-11-01'  -- Glass: Start from Nov 1, 2024
            ELSE '2022-08-31'    -- Others: Start from Aug 31, 2022
        END AS StartDate
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
    WHERE werks LIKE 'DE%'
      AND matnr BETWEEN 7546978 AND 7597095
),

OpeningBalance AS (
    -- Calculate opening balance: Sum all movements from beginning of time up to start date
    SELECT 
        mc.Material,
        mc.Category,
        mc.StartDate,
        mseg.werks AS Plant,
        mseg.lgort AS StorageLocation,
        SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS OpeningBalance
    FROM MaterialCategories mc
    JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg mseg
        ON mc.Material = mseg.matnr
    WHERE mseg.werks LIKE 'DE%'
      AND mseg.cpudt_mkpf < mc.StartDate  -- Before start date
    GROUP BY mc.Material, mc.Category, mc.StartDate, mseg.werks, mseg.lgort
),

MovementsAfterStart AS (
    -- Calculate receipts and issues after the start date
    SELECT 
        mc.Material,
        mc.Category,
        mc.StartDate,
        mseg.werks AS Plant,
        mseg.lgort AS StorageLocation,
        SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE 0 END) AS Receipts,
        SUM(CASE WHEN mseg.shkzg = 'H' THEN mseg.menge ELSE 0 END) AS Issues,
        SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS NetMovements
    FROM MaterialCategories mc
    JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg mseg
        ON mc.Material = mseg.matnr
    WHERE mseg.werks LIKE 'DE%'
      AND mseg.cpudt_mkpf >= mc.StartDate  -- On or after start date
    GROUP BY mc.Material, mc.Category, mc.StartDate, mseg.werks, mseg.lgort
),

CalculatedStock AS (
    -- Calculate final closing stock using the formula:
    -- Closing Stock = Opening Balance + Receipts - Issues
    SELECT 
        mc.Material AS SKU,
        mc.Category,
        mc.StartDate,
        COALESCE(ob.Plant, mas.Plant) AS Plant,
        COALESCE(ob.StorageLocation, mas.StorageLocation) AS StorageLocation,
        COALESCE(ob.OpeningBalance, 0) AS OpeningBalance,
        COALESCE(mas.Receipts, 0) AS Receipts,
        COALESCE(mas.Issues, 0) AS Issues,
        COALESCE(mas.NetMovements, 0) AS NetMovements,
        -- Final calculated stock
        COALESCE(ob.OpeningBalance, 0) + COALESCE(mas.NetMovements, 0) AS CalculatedClosingStock
    FROM MaterialCategories mc
    FULL OUTER JOIN OpeningBalance ob ON mc.Material = ob.Material
    FULL OUTER JOIN MovementsAfterStart mas ON mc.Material = mas.Material 
        AND COALESCE(ob.Plant, 'NULL') = COALESCE(mas.Plant, 'NULL')
        AND COALESCE(ob.StorageLocation, 'NULL') = COALESCE(mas.StorageLocation, 'NULL')
),

-- Get additional material information
MaterialInfo AS (
    SELECT 
        mard.matnr AS SKU,
        mard.werks AS Plant,
        makt.maktx AS MaterialDescription,
        mbew.verpr AS MovingPrice,
        mbew.peinh AS PriceUnit,
        mard.labst AS CurrentMARDStock  -- For comparison
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard AS mard
    LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS mbew
        ON mard.matnr = mbew.matnr AND mard.werks = mbew.bwkey
    LEFT JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS makt
        ON mard.matnr = makt.matnr AND makt.spras = 'E'
    WHERE mard.werks LIKE 'DE%'
      AND mard.matnr BETWEEN 7546978 AND 7597095
)

-- Final result with corrected stock calculation
SELECT 
    'DE' AS Country,
    cs.SKU,
    mi.MaterialDescription,
    cs.StorageLocation,
    cs.Plant,
    cs.Category,
    cs.StartDate,
    cs.OpeningBalance,
    cs.Receipts,
    cs.Issues,
    cs.NetMovements,
    cs.CalculatedClosingStock AS TotalStock,  -- This is the corrected stock quantity
    mi.CurrentMARDStock,  -- For comparison with old method
    cs.CalculatedClosingStock - mi.CurrentMARDStock AS StockDifference,
    mi.MovingPrice / NULLIF(mi.PriceUnit, 0) AS Price,
    cs.CalculatedClosingStock * (mi.MovingPrice / NULLIF(mi.PriceUnit, 0)) AS TotalStockValue,
    CAST('2025-07-31' AS DATE) AS Today
FROM CalculatedStock cs
LEFT JOIN MaterialInfo mi ON cs.SKU = mi.SKU AND cs.Plant = mi.Plant
WHERE cs.CalculatedClosingStock > 0  -- Only show materials with positive stock
ORDER BY cs.Plant, cs.StorageLocation, cs.SKU;

-- ============================================================================
-- EXPLANATION OF THE CORRECTED METHODOLOGY:
-- 
-- 1. MaterialCategories: Determines if each material is "Glass" or "Others"
--    - Glass materials use start date: 2024-11-01
--    - Others materials use start date: 2022-08-31
--
-- 2. OpeningBalance: Sums all movements from beginning of time up to start date
--    - This captures the historical stock position at the baseline
--
-- 3. MovementsAfterStart: Sums receipts (S) and issues (H) after start date
--    - Receipts: All 'S' movements (stock increases)
--    - Issues: All 'H' movements (stock decreases)
--
-- 4. CalculatedStock: Applies the formula:
--    Closing Stock = Opening Balance + Net Movements After Start Date
--
-- 5. Final result includes both the corrected stock and the old MARD stock
--    for comparison, showing the difference between methods.
-- ============================================================================
