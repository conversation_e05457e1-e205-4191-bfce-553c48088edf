import pandas as pd
import logging
from databricks import sql

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_query(query: str, query_name: str, connection) -> pd.DataFrame:
    """Execute a SQL query and return results as DataFrame."""
    try:
        logging.info(f"Executing {query_name}...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        
        df = pd.DataFrame(data, columns=columns)
        logging.info(f"{query_name} completed. Fetched {len(df)} rows.")
        return df
    except Exception as e:
        logging.error(f"Error executing {query_name}: {e}")
        return pd.DataFrame()

def test_different_start_dates(connection):
    """Test different start dates to find the correct baseline"""
    logging.info("\n" + "="*80)
    logging.info("TESTING DIFFERENT START DATES FOR SKU 7546978")
    logging.info("="*80)
    
    # Test different start dates
    test_dates = [
        '2018-01-01',  # Very early
        '2020-01-01',  # Early
        '2022-01-01',  # Medium
        '2022-08-31',  # Others category date
        '2023-01-01',  # Recent
        '2024-01-01',  # Very recent
        '2024-11-01',  # Glass category date
        '2025-01-01'   # Very recent
    ]
    
    results = []
    
    for start_date in test_dates:
        query = f"""
        SELECT 
            '{start_date}' AS StartDate,
            SUM(CASE WHEN cpudt_mkpf < '{start_date}' THEN 
                (CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) ELSE 0 END) AS OpeningBalance,
            SUM(CASE WHEN cpudt_mkpf >= '{start_date}' AND shkzg = 'S' THEN menge ELSE 0 END) AS Receipts,
            SUM(CASE WHEN cpudt_mkpf >= '{start_date}' AND shkzg = 'H' THEN menge ELSE 0 END) AS Issues,
            SUM(CASE WHEN cpudt_mkpf >= '{start_date}' THEN 
                (CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) ELSE 0 END) AS NetMovements,
            SUM(CASE WHEN cpudt_mkpf < '{start_date}' THEN 
                (CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) ELSE 0 END) +
            SUM(CASE WHEN cpudt_mkpf >= '{start_date}' THEN 
                (CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) ELSE 0 END) AS CalculatedStock,
            23070.0 AS TargetStock,
            (SUM(CASE WHEN cpudt_mkpf < '{start_date}' THEN 
                (CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) ELSE 0 END) +
            SUM(CASE WHEN cpudt_mkpf >= '{start_date}' THEN 
                (CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) ELSE 0 END)) - 23070.0 AS DifferenceFromTarget
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        WHERE matnr = '000000000007546978'
          AND werks = 'DE30'
        """
        
        result = execute_query(query, f"Test Start Date {start_date}", connection)
        
        if not result.empty:
            row = result.iloc[0]
            opening_balance = float(row['OpeningBalance'])
            receipts = float(row['Receipts'])
            issues = float(row['Issues'])
            net_movements = float(row['NetMovements'])
            calculated_stock = float(row['CalculatedStock'])
            diff_from_target = float(row['DifferenceFromTarget'])
            
            logging.info(f"Start Date: {start_date}")
            logging.info(f"  Opening Balance: {opening_balance:,.0f}")
            logging.info(f"  Receipts After: {receipts:,.0f}")
            logging.info(f"  Issues After: {issues:,.0f}")
            logging.info(f"  Net Movements After: {net_movements:,.0f}")
            logging.info(f"  CALCULATED STOCK: {calculated_stock:,.0f}")
            logging.info(f"  Difference from Target: {diff_from_target:+,.0f}")
            
            if abs(diff_from_target) < 10:
                logging.info(f"  ✅ PERFECT MATCH! Start date {start_date} gives target stock!")
            elif abs(diff_from_target) < 500:
                logging.info(f"  ✅ CLOSE MATCH! Start date {start_date} is very close to target")
            
            logging.info("")
            
            results.append({
                'StartDate': start_date,
                'OpeningBalance': opening_balance,
                'CalculatedStock': calculated_stock,
                'DifferenceFromTarget': diff_from_target,
                'AbsDifference': abs(diff_from_target)
            })
    
    # Find the best start date
    if results:
        best_result = min(results, key=lambda x: x['AbsDifference'])
        logging.info(f"🎯 BEST START DATE: {best_result['StartDate']}")
        logging.info(f"   Calculated Stock: {best_result['CalculatedStock']:,.0f}")
        logging.info(f"   Difference from Target: {best_result['DifferenceFromTarget']:+,.0f}")
        
        return best_result['StartDate']
    
    return None

def test_zero_opening_balance_theory(connection):
    """Test the theory that we should use zero opening balance"""
    logging.info("\n" + "="*80)
    logging.info("TESTING ZERO OPENING BALANCE THEORY")
    logging.info("Maybe the target assumes zero opening balance?")
    logging.info("="*80)
    
    query = """
    SELECT 
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS TotalNetMovements,
        23070.0 AS TargetStock,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) - 23070.0 AS DifferenceFromTarget,
        COUNT(*) AS TotalMovements,
        MIN(cpudt_mkpf) AS EarliestMovement,
        MAX(cpudt_mkpf) AS LatestMovement
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    """
    
    result = execute_query(query, "Zero Opening Balance Test", connection)
    
    if not result.empty:
        row = result.iloc[0]
        total_movements = float(row['TotalNetMovements'])
        diff_from_target = float(row['DifferenceFromTarget'])
        
        logging.info(f"Total Net Movements (all time): {total_movements:,.0f}")
        logging.info(f"Target Stock: 23,070")
        logging.info(f"Difference: {diff_from_target:+,.0f}")
        logging.info(f"Movement Date Range: {row['EarliestMovement']} to {row['LatestMovement']}")
        
        if abs(diff_from_target) < 10:
            logging.info("✅ ZERO OPENING BALANCE THEORY WORKS!")
            return True
        else:
            logging.info("❌ Zero opening balance doesn't match target")
    
    return False

def main():
    """Main function to test different approaches"""
    logging.info("=== TESTING DIFFERENT START DATE APPROACHES ===")
    
    try:
        connection = get_databricks_connection()
        
        # Test different start dates
        best_start_date = test_different_start_dates(connection)
        
        # Test zero opening balance theory
        zero_works = test_zero_opening_balance_theory(connection)
        
        # Final recommendation
        logging.info("\n" + "="*80)
        logging.info("FINAL RECOMMENDATION")
        logging.info("="*80)
        
        if zero_works:
            logging.info("🎯 SOLUTION: Use zero opening balance (sum all movements)")
            logging.info("   The target appears to be calculated as total net movements from beginning")
        elif best_start_date:
            logging.info(f"🎯 SOLUTION: Use start date {best_start_date}")
            logging.info("   This gives the closest match to the target")
        else:
            logging.info("❓ No perfect solution found")
            logging.info("   May need to investigate the source of the target figure")
        
        connection.close()
        
    except Exception as e:
        logging.error(f"Error during testing: {e}")

if __name__ == '__main__':
    main()
