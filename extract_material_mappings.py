import pandas as pd
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def extract_material_mappings():
    """Extract the complete material category mappings"""
    try:
        excel_file = 'DE Temp POCM Calculation P07 2025_TL.xlsx'
        
        # Read the Glass and others sheet
        glass_others_df = pd.read_excel(excel_file, sheet_name='Glass and others')
        logging.info(f"Glass and others sheet shape: {glass_others_df.shape}")
        
        # Clean and get unique mappings
        glass_others_df.columns = ['Material', 'Category']
        glass_others_df = glass_others_df.dropna()
        unique_mappings = glass_others_df.drop_duplicates(subset=['Material'])
        
        logging.info(f"Unique material-category mappings: {len(unique_mappings)}")
        
        # Show category distribution
        category_counts = unique_mappings['Category'].value_counts()
        logging.info(f"Category distribution:")
        for category, count in category_counts.items():
            logging.info(f"  {category}: {count} materials")
        
        # Show sample mappings for each category
        for category in unique_mappings['Category'].unique():
            sample_materials = unique_mappings[unique_mappings['Category'] == category]['Material'].head(5).tolist()
            logging.info(f"Sample {category} materials: {sample_materials}")
        
        # Also check Category 2 sheet for material group information
        logging.info("\n" + "="*50)
        logging.info("CHECKING CATEGORY 2 SHEET FOR MATERIAL GROUPS")
        logging.info("="*50)
        
        category2_df = pd.read_excel(excel_file, sheet_name='Category 2')
        logging.info(f"Category 2 sheet shape: {category2_df.shape}")
        
        # Look for material group column
        relevant_cols = ['Material', 'Matl Group', 'Category']
        if all(col in category2_df.columns for col in relevant_cols):
            material_groups = category2_df[relevant_cols].dropna()
            
            # Show unique material groups and their categories
            group_category = material_groups.groupby(['Matl Group', 'Category']).size().reset_index(name='Count')
            logging.info("Material Group to Category mapping:")
            logging.info(group_category.to_string())
            
            # Check if we can find the Z001-Z006 pattern mentioned
            z_groups = material_groups[material_groups['Matl Group'].astype(str).str.startswith('Z')]
            if not z_groups.empty:
                logging.info("\nFound Z-groups:")
                z_group_summary = z_groups.groupby(['Matl Group', 'Category']).size().reset_index(name='Count')
                logging.info(z_group_summary.to_string())
            else:
                logging.info("No Z-groups found in this sheet")
        
        return unique_mappings
        
    except Exception as e:
        logging.error(f"Error extracting material mappings: {e}")
        return pd.DataFrame()

if __name__ == '__main__':
    logging.info("=== EXTRACTING MATERIAL CATEGORY MAPPINGS ===")
    mappings_df = extract_material_mappings()
    
    if not mappings_df.empty:
        # Save the mappings for use in the corrected script
        mappings_df.to_csv('material_category_mappings.csv', index=False)
        logging.info("✅ Material category mappings saved to 'material_category_mappings.csv'")
    else:
        logging.info("❌ Could not extract material category mappings")
