import pandas as pd
import logging
from databricks import sql

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_query(query: str, query_name: str, connection) -> pd.DataFrame:
    """Execute a SQL query and return results as DataFrame."""
    try:
        logging.info(f"Executing {query_name}...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        
        df = pd.DataFrame(data, columns=columns)
        logging.info(f"{query_name} completed. Fetched {len(df)} rows.")
        return df
    except Exception as e:
        logging.error(f"Error executing {query_name}: {e}")
        return pd.DataFrame()

def explore_sku_formats(connection):
    """Explore different SKU formats in the database"""
    logging.info("\n" + "="*80)
    logging.info("EXPLORING SKU FORMATS")
    logging.info("="*80)
    
    # Check if SKU exists with different formats
    formats_to_test = [
        "7546978",
        "07546978", 
        "0007546978",
        "000007546978"
    ]
    
    for sku_format in formats_to_test:
        query = f"""
        SELECT 
            matnr AS SKU,
            werks AS Plant,
            COUNT(*) as Record_Count
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
        WHERE matnr = '{sku_format}'
          AND werks LIKE 'DE%'
        GROUP BY matnr, werks
        LIMIT 5
        """
        
        result = execute_query(query, f"SKU Format Test: {sku_format}", connection)
        
        if not result.empty:
            logging.info(f"✅ Found data for SKU format: {sku_format}")
            logging.info(result.to_string())
            return sku_format
        else:
            logging.info(f"❌ No data for SKU format: {sku_format}")
    
    return None

def explore_available_skus(connection):
    """Explore what SKUs are actually available in the database"""
    logging.info("\n" + "="*80)
    logging.info("EXPLORING AVAILABLE SKUs")
    logging.info("="*80)
    
    # Get SKUs in the range we're interested in
    query = """
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        labst AS Stock,
        COUNT(*) OVER (PARTITION BY matnr) as Plant_Count
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
    WHERE werks LIKE 'DE%'
      AND CAST(matnr AS BIGINT) BETWEEN 7546970 AND 7546985
      AND labst > 0
    ORDER BY CAST(matnr AS BIGINT), werks
    LIMIT 20
    """
    
    result = execute_query(query, "Available SKUs in Range", connection)
    
    if not result.empty:
        logging.info("Available SKUs in the range 7546970-7546985:")
        logging.info(result.to_string())
        return result
    else:
        logging.info("No SKUs found in the specified range")
        
        # Try a broader search
        query_broad = """
        SELECT 
            matnr AS SKU,
            werks AS Plant,
            labst AS Stock
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
        WHERE werks = 'DE30'
          AND labst > 20000
        ORDER BY labst DESC
        LIMIT 10
        """
        
        result_broad = execute_query(query_broad, "High Stock SKUs at DE30", connection)
        
        if not result_broad.empty:
            logging.info("High stock SKUs at Plant DE30:")
            logging.info(result_broad.to_string())
            return result_broad
    
    return pd.DataFrame()

def test_current_script_logic(connection, test_sku):
    """Test the current script logic with a known SKU"""
    logging.info(f"\n" + "="*80)
    logging.info(f"TESTING CURRENT SCRIPT LOGIC WITH SKU: {test_sku}")
    logging.info("="*80)
    
    query = f"""
    WITH CurrentStockAndValue AS (
        SELECT
            mard.matnr AS SKU,
            mard.werks AS Plant,
            makt.maktx AS MaterialDescription,
            mbew.verpr AS MovingPrice,
            mbew.peinh AS PriceUnit,
            mard.labst AS TotalStock,
            (mard.labst * (mbew.verpr / NULLIF(mbew.peinh, 0))) AS TotalStockValue
        FROM
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard AS mard
        LEFT JOIN
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mbew AS mbew
            ON mard.matnr = mbew.matnr AND mard.werks = mbew.bwkey
        LEFT JOIN
            brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.makt AS makt
            ON mard.matnr = makt.matnr AND makt.spras = 'E'
        WHERE
            mard.werks = 'DE30'
            AND mard.matnr = '{test_sku}'
    )
    SELECT
        SKU,
        Plant,
        MaterialDescription,
        TotalStock,
        TotalStockValue,
        MovingPrice,
        PriceUnit
    FROM CurrentStockAndValue
    """
    
    result = execute_query(query, f"Current Script Logic Test - {test_sku}", connection)
    
    if not result.empty:
        logging.info(f"Current script would produce for SKU {test_sku}:")
        logging.info(result.to_string())
        return result
    else:
        logging.info(f"No data found for SKU {test_sku} with current script logic")
    
    return pd.DataFrame()

def main():
    """Main exploration function"""
    logging.info("=== SAP DATA STRUCTURE EXPLORATION ===")
    
    try:
        connection = get_databricks_connection()
        
        # First, try to find the correct SKU format
        correct_sku_format = explore_sku_formats(connection)
        
        if correct_sku_format:
            logging.info(f"\n🎯 Found correct SKU format: {correct_sku_format}")
            # Test with the correct format
            test_current_script_logic(connection, correct_sku_format)
        else:
            # If we can't find the specific SKU, explore what's available
            available_skus = explore_available_skus(connection)
            
            if not available_skus.empty:
                # Test with the first available SKU
                test_sku = available_skus.iloc[0]['SKU']
                logging.info(f"\n🔍 Testing with available SKU: {test_sku}")
                test_current_script_logic(connection, test_sku)
        
        connection.close()
        
    except Exception as e:
        logging.error(f"Error during exploration: {e}")

if __name__ == '__main__':
    main()
