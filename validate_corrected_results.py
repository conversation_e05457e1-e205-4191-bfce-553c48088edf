import pandas as pd
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def validate_corrected_results():
    """Validate the corrected results against the target"""
    try:
        # Load the corrected output
        corrected_file = 'PBI_Output_CORRECTED_Opening_Balance_Method.csv'
        if not pd.io.common.file_exists(corrected_file):
            logging.error(f"Corrected output file not found: {corrected_file}")
            return
        
        corrected_df = pd.read_csv(corrected_file)
        logging.info(f"Loaded corrected results: {len(corrected_df)} records")
        
        # Show basic info about the corrected data
        logging.info(f"Columns: {list(corrected_df.columns)}")
        logging.info(f"Data types:")
        logging.info(corrected_df.dtypes)
        
        # Load the comparison data (target)
        comparison_df = pd.read_csv('material_category_mappings.csv')
        logging.info(f"Loaded material categories: {len(comparison_df)} materials")
        
        # Load the original comparison file to get targets
        try:
            comparison_file = 'Comparision.xlsx'
            target_df = pd.read_excel(comparison_file, sheet_name='Sheet2')
            target_df.columns = ['SKU', 'Plant', 'Target_Stock', 'Current_PBI_Stock', 'Difference']
            target_df = target_df.dropna(subset=['SKU', 'Plant', 'Target_Stock'])
            target_df['SKU'] = target_df['SKU'].astype(str).str.strip()
            target_df['Plant'] = target_df['Plant'].astype(str).str.strip()
            logging.info(f"Loaded target data: {len(target_df)} records")
            
            # Test our specific case: SKU 7546978 at Plant DE30
            test_sku = "7546978"
            test_plant = "DE30"
            target_stock = 23070.0
            
            # Find in corrected results
            corrected_df['SKU'] = corrected_df['SKU'].astype(str).str.strip()
            corrected_df['Plant'] = corrected_df['Plant'].astype(str).str.strip()
            
            # Look for the test case in corrected results
            test_case_corrected = corrected_df[
                (corrected_df['SKU'].str.contains(test_sku, na=False)) & 
                (corrected_df['Plant'] == test_plant)
            ]
            
            if not test_case_corrected.empty:
                corrected_stock = test_case_corrected['Total Stock'].iloc[0]
                old_mard_stock = test_case_corrected['OldMARDStock'].iloc[0]
                stock_diff = test_case_corrected['StockDifference'].iloc[0]
                category = test_case_corrected['Impairment Category'].iloc[0]
                start_date = test_case_corrected['StartDate'].iloc[0]
                opening_balance = test_case_corrected['OpeningBalance'].iloc[0]
                
                logging.info(f"\n" + "="*80)
                logging.info(f"VALIDATION RESULTS FOR SKU {test_sku} AT PLANT {test_plant}")
                logging.info(f"="*80)
                logging.info(f"Target Stock (Ground Truth): {target_stock:,.0f} units")
                logging.info(f"Old MARD Method: {old_mard_stock:,.0f} units")
                logging.info(f"Corrected Method: {corrected_stock:,.0f} units")
                logging.info(f"Category: {category}")
                logging.info(f"Start Date: {start_date}")
                logging.info(f"Opening Balance: {opening_balance:,.0f} units")
                logging.info(f"")
                logging.info(f"Difference vs Target: {corrected_stock - target_stock:+,.0f} units")
                logging.info(f"Difference vs Old Method: {stock_diff:+,.0f} units")
                
                if abs(corrected_stock - target_stock) < 10:
                    logging.info(f"✅ SUCCESS! Corrected method matches target within tolerance")
                elif abs(corrected_stock - target_stock) < abs(old_mard_stock - target_stock):
                    logging.info(f"✅ IMPROVEMENT! Corrected method is closer to target")
                else:
                    logging.info(f"❌ Still not matching target exactly")
                
            else:
                logging.warning(f"Test case SKU {test_sku} at Plant {test_plant} not found in corrected results")
                
                # Show available SKUs for debugging
                available_skus = corrected_df['SKU'].dropna().unique()[:10]
                logging.info(f"Available SKUs (first 10): {available_skus}")
                
                # Try broader search
                broader_search = corrected_df[corrected_df['Plant'] == test_plant]
                if not broader_search.empty:
                    logging.info(f"Found {len(broader_search)} records for Plant {test_plant}")
                    sample_records = broader_search[['SKU', 'Plant', 'Total Stock', 'Impairment Category']].head(5)
                    logging.info("Sample records:")
                    logging.info(sample_records.to_string())
            
            # Show overall statistics
            logging.info(f"\n" + "="*80)
            logging.info(f"OVERALL CORRECTED RESULTS SUMMARY")
            logging.info(f"="*80)
            
            if 'Impairment Category' in corrected_df.columns:
                category_summary = corrected_df['Impairment Category'].value_counts()
                logging.info(f"Materials by category:")
                for category, count in category_summary.items():
                    logging.info(f"  {category}: {count:,} records")
            
            if 'Total Stock' in corrected_df.columns:
                total_stock = corrected_df['Total Stock'].sum()
                logging.info(f"Total corrected stock: {total_stock:,.0f} units")
            
            if 'StockDifference' in corrected_df.columns:
                total_diff = corrected_df['StockDifference'].sum()
                avg_diff = corrected_df['StockDifference'].mean()
                logging.info(f"Total difference vs old method: {total_diff:,.0f} units")
                logging.info(f"Average difference per record: {avg_diff:,.0f} units")
                
        except Exception as e:
            logging.error(f"Error loading comparison data: {e}")
        
    except Exception as e:
        logging.error(f"Error validating corrected results: {e}")

if __name__ == '__main__':
    logging.info("=== VALIDATING CORRECTED STOCK CALCULATION RESULTS ===")
    validate_corrected_results()
