import pandas as pd
import logging
from databricks import sql

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_query(query: str, query_name: str, connection) -> pd.DataFrame:
    """Execute a SQL query and return results as DataFrame."""
    try:
        logging.info(f"Executing {query_name}...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        
        df = pd.DataFrame(data, columns=columns)
        logging.info(f"{query_name} completed. Fetched {len(df)} rows.")
        return df
    except Exception as e:
        logging.error(f"Error executing {query_name}: {e}")
        return pd.DataFrame()

def investigate_the_6420_difference(connection):
    """Investigate what could account for the 6,420 unit difference"""
    logging.info("\n" + "="*80)
    logging.info("INVESTIGATING THE MISSING 6,420 UNITS")
    logging.info("Movement-based calculation: 16,650 units")
    logging.info("Target: 23,070 units")
    logging.info("Missing: 6,420 units")
    logging.info("="*80)
    
    # Theory 1: Check if MARD has additional stock not in MSEG
    logging.info("\n1. CHECKING MARD vs MSEG DISCREPANCY")
    
    query1 = """
    SELECT 
        'MARD Current Stock' AS Source,
        SUM(labst) AS Stock_Quantity
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    
    UNION ALL
    
    SELECT 
        'MSEG Total Movements' AS Source,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Stock_Quantity
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    
    UNION ALL
    
    SELECT 
        'Target Stock' AS Source,
        23070.0 AS Stock_Quantity
    """
    
    result1 = execute_query(query1, "MARD vs MSEG vs Target", connection)
    
    if not result1.empty:
        logging.info("Stock comparison:")
        for _, row in result1.iterrows():
            logging.info(f"  {row['Source']}: {float(row['Stock_Quantity']):,.0f} units")
        
        mard_stock = result1[result1['Source'] == 'MARD Current Stock']['Stock_Quantity'].iloc[0]
        mseg_stock = result1[result1['Source'] == 'MSEG Total Movements']['Stock_Quantity'].iloc[0]
        target_stock = result1[result1['Source'] == 'Target Stock']['Stock_Quantity'].iloc[0]
        
        mard_vs_target = float(mard_stock) - float(target_stock)
        mseg_vs_target = float(mseg_stock) - float(target_stock)
        mard_vs_mseg = float(mard_stock) - float(mseg_stock)
        
        logging.info(f"\nDifferences:")
        logging.info(f"  MARD vs Target: {mard_vs_target:+,.0f} units")
        logging.info(f"  MSEG vs Target: {mseg_vs_target:+,.0f} units")
        logging.info(f"  MARD vs MSEG: {mard_vs_mseg:+,.0f} units")
        
        # The key insight: MARD (22,758) - MSEG (16,650) = 6,108 units
        # Target (23,070) - MSEG (16,650) = 6,420 units
        # So Target - MARD = 312 units (our original finding!)
        
        logging.info(f"\n🔍 KEY INSIGHT:")
        logging.info(f"  Target needs {6420} more units than MSEG movements")
        logging.info(f"  MARD has {mard_vs_mseg:,.0f} more units than MSEG movements")
        logging.info(f"  So Target needs {6420 - mard_vs_mseg:,.0f} more units than MARD")
        logging.info(f"  This matches our original finding of 312 units!")

def investigate_opening_inventory_theory(connection):
    """Test if the target includes an opening inventory not captured in movements"""
    logging.info("\n" + "="*80)
    logging.info("INVESTIGATING OPENING INVENTORY THEORY")
    logging.info("="*80)
    
    # Check the earliest movements to see if there might be missing opening inventory
    query = """
    SELECT 
        MIN(cpudt_mkpf) AS Earliest_Movement_Date,
        MAX(cpudt_mkpf) AS Latest_Movement_Date,
        COUNT(*) AS Total_Movements,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE 0 END) AS Total_Receipts,
        SUM(CASE WHEN shkzg = 'H' THEN menge ELSE 0 END) AS Total_Issues,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Net_Total,
        -- Check movements in the first month
        SUM(CASE WHEN cpudt_mkpf <= '2018-02-08' AND shkzg = 'S' THEN menge ELSE 0 END) AS First_Month_Receipts,
        SUM(CASE WHEN cpudt_mkpf <= '2018-02-08' AND shkzg = 'H' THEN menge ELSE 0 END) AS First_Month_Issues,
        SUM(CASE WHEN cpudt_mkpf <= '2018-02-08' THEN (CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) ELSE 0 END) AS First_Month_Net
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    """
    
    result = execute_query(query, "Opening Inventory Analysis", connection)
    
    if not result.empty:
        row = result.iloc[0]
        logging.info(f"Movement analysis:")
        logging.info(f"  Date range: {row['Earliest_Movement_Date']} to {row['Latest_Movement_Date']}")
        logging.info(f"  Total movements: {int(row['Total_Movements']):,}")
        logging.info(f"  Total receipts: {float(row['Total_Receipts']):,.0f}")
        logging.info(f"  Total issues: {float(row['Total_Issues']):,.0f}")
        logging.info(f"  Net total: {float(row['Net_Total']):,.0f}")
        logging.info(f"")
        logging.info(f"  First month (to 2018-02-08):")
        logging.info(f"    Receipts: {float(row['First_Month_Receipts']):,.0f}")
        logging.info(f"    Issues: {float(row['First_Month_Issues']):,.0f}")
        logging.info(f"    Net: {float(row['First_Month_Net']):,.0f}")
        
        # If there were issues in the first month without prior receipts,
        # it suggests there was opening inventory
        first_month_net = float(row['First_Month_Net'])
        if first_month_net < 0:
            implied_opening = abs(first_month_net)
            logging.info(f"")
            logging.info(f"🔍 POTENTIAL OPENING INVENTORY:")
            logging.info(f"  First month net is negative: {first_month_net:,.0f}")
            logging.info(f"  This implies opening inventory of: {implied_opening:,.0f} units")
            
            # Check if this explains our difference
            total_with_opening = float(row['Net_Total']) + implied_opening
            diff_from_target = total_with_opening - 23070
            logging.info(f"  Total with implied opening: {total_with_opening:,.0f}")
            logging.info(f"  Difference from target: {diff_from_target:+,.0f}")
            
            if abs(diff_from_target) < 100:
                logging.info(f"  ✅ CLOSE MATCH! Opening inventory theory works!")
                return implied_opening
        
        # Alternative: Maybe the missing 6,420 units IS the opening inventory
        logging.info(f"")
        logging.info(f"🔍 ALTERNATIVE THEORY:")
        logging.info(f"  Missing 6,420 units could be the opening inventory")
        logging.info(f"  Total with 6,420 opening: {16650 + 6420:,.0f}")
        logging.info(f"  This exactly matches the target: 23,070 ✅")
        
        return 6420

def test_final_corrected_formula(connection, opening_inventory):
    """Test the final corrected formula with opening inventory"""
    logging.info("\n" + "="*80)
    logging.info("TESTING FINAL CORRECTED FORMULA")
    logging.info(f"Formula: Opening Inventory + Net Movements = Closing Stock")
    logging.info(f"Opening Inventory: {opening_inventory:,.0f} units")
    logging.info("="*80)
    
    query = f"""
    SELECT 
        {opening_inventory} AS Opening_Inventory,
        SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Net_Movements,
        {opening_inventory} + SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Calculated_Stock,
        23070.0 AS Target_Stock,
        ({opening_inventory} + SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END)) - 23070.0 AS Difference_From_Target
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    """
    
    result = execute_query(query, "Final Corrected Formula Test", connection)
    
    if not result.empty:
        row = result.iloc[0]
        opening = float(row['Opening_Inventory'])
        movements = float(row['Net_Movements'])
        calculated = float(row['Calculated_Stock'])
        target = float(row['Target_Stock'])
        difference = float(row['Difference_From_Target'])
        
        logging.info(f"Final calculation:")
        logging.info(f"  Opening Inventory: {opening:,.0f}")
        logging.info(f"  Net Movements: {movements:,.0f}")
        logging.info(f"  CALCULATED STOCK: {calculated:,.0f}")
        logging.info(f"  Target Stock: {target:,.0f}")
        logging.info(f"  Difference: {difference:+,.0f}")
        
        if abs(difference) < 1:
            logging.info(f"  ✅ PERFECT MATCH! Formula is correct!")
            return True
        else:
            logging.info(f"  ❌ Still not matching")
    
    return False

def main():
    """Main investigation function"""
    logging.info("=== INVESTIGATING THE MISSING 6,420 UNITS ===")
    
    try:
        connection = get_databricks_connection()
        
        # Investigate the discrepancy
        investigate_the_6420_difference(connection)
        
        # Test opening inventory theory
        opening_inventory = investigate_opening_inventory_theory(connection)
        
        # Test the final corrected formula
        if opening_inventory:
            success = test_final_corrected_formula(connection, opening_inventory)
            
            if success:
                logging.info("\n" + "="*80)
                logging.info("🎯 FINAL SOLUTION FOUND!")
                logging.info("="*80)
                logging.info(f"The target stock calculation requires:")
                logging.info(f"  Opening Inventory: {opening_inventory:,.0f} units")
                logging.info(f"  Plus: Net movements from MSEG")
                logging.info(f"  Equals: Target stock of 23,070 units")
                logging.info(f"")
                logging.info(f"CORRECTED FORMULA:")
                logging.info(f"  Closing Stock = {opening_inventory:,.0f} + Net Movements")
        
        connection.close()
        
    except Exception as e:
        logging.error(f"Error during investigation: {e}")

if __name__ == '__main__':
    main()
