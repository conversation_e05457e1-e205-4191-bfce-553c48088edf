import pandas as pd
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def load_comparison_data():
    """
    Load the comparison data from the Excel file.
    """
    try:
        # Try to read the comparison file
        comparison_file = 'Comparision.xlsx'
        
        if os.path.exists(comparison_file):
            logging.info("Loading comparison data from Excel file...")
            
            # Try different sheet names and approaches
            try:
                # First try to see what sheets are available
                xl_file = pd.ExcelFile(comparison_file)
                logging.info(f"Available sheets: {xl_file.sheet_names}")
                
                # Try Sheet2 first
                if 'Sheet2' in xl_file.sheet_names:
                    df = pd.read_excel(comparison_file, sheet_name='Sheet2')
                    logging.info("Successfully loaded data from 'Sheet2'")
                elif len(xl_file.sheet_names) > 1:
                    df = pd.read_excel(comparison_file, sheet_name=xl_file.sheet_names[1])
                    logging.info(f"Successfully loaded data from '{xl_file.sheet_names[1]}'")
                else:
                    df = pd.read_excel(comparison_file)
                    logging.info("Successfully loaded data from default sheet")
                
            except Exception as e:
                logging.error(f"Error reading Excel file: {e}")
                return pd.DataFrame()
            
            logging.info(f"DataFrame shape: {df.shape}")
            logging.info(f"Column names: {list(df.columns)}")
            
            # Show first few rows
            logging.info("First 5 rows:")
            logging.info(df.head().to_string())
            
            # Look for relevant columns - based on the actual data structure
            sku_col = ' Material'  # The actual SKU column (with leading space)
            plant_col = 'Plant'
            target_stock_col = 'Closing Stock'  # This appears to be the ground truth
            current_stock_col = 'PBI Stock'  # This is our current script output
            difference_col = 'Difference'  # This shows the discrepancy

            logging.info(f"Using columns - SKU: '{sku_col}', Plant: '{plant_col}', Target Stock: '{target_stock_col}'")

            # Verify these columns exist
            missing_cols = []
            for col_name, col_var in [('SKU', sku_col), ('Plant', plant_col), ('Target Stock', target_stock_col)]:
                if col_var not in df.columns:
                    missing_cols.append(f"{col_name} ('{col_var}')")

            if missing_cols:
                logging.error(f"Missing columns: {missing_cols}")
                return pd.DataFrame()
            
            # Create the result dataframe with additional columns for analysis
            result_df = df[[sku_col, plant_col, target_stock_col, current_stock_col, difference_col]].copy()
            result_df.columns = ['SKU', 'Plant', 'Target_Stock_Quantity', 'Current_PBI_Stock', 'Stock_Difference']

            # Show data types before cleaning
            logging.info("Data types before cleaning:")
            logging.info(result_df.dtypes)

            # Clean the data
            result_df.dropna(subset=['SKU', 'Plant', 'Target_Stock_Quantity'], inplace=True)

            # Convert SKU to string and remove any leading zeros for consistency
            result_df['SKU'] = result_df['SKU'].astype(str).str.strip()
            result_df['Plant'] = result_df['Plant'].astype(str).str.strip()

            logging.info(f"Successfully loaded {len(result_df)} comparison records")
            logging.info("Sample of cleaned data:")
            logging.info(result_df.head(10).to_string())

            # Look for our test case
            test_sku = "7546978"
            test_plant = "DE30"
            test_case = result_df[(result_df['SKU'] == test_sku) & (result_df['Plant'] == test_plant)]

            if not test_case.empty:
                logging.info(f"Found test case - SKU: {test_sku}, Plant: {test_plant}")
                logging.info(f"  Target Stock: {test_case['Target_Stock_Quantity'].iloc[0]}")
                logging.info(f"  Current PBI Stock: {test_case['Current_PBI_Stock'].iloc[0]}")
                logging.info(f"  Difference: {test_case['Stock_Difference'].iloc[0]}")
            else:
                logging.info(f"Test case SKU {test_sku} at Plant {test_plant} not found")
                # Show available SKUs and Plants
                logging.info(f"Available SKUs (first 10): {result_df['SKU'].unique()[:10]}")
                logging.info(f"Available Plants: {result_df['Plant'].unique()}")

            # Show some records with differences for analysis
            differences = result_df[result_df['Stock_Difference'].notna() & (result_df['Stock_Difference'] != 0)]
            if not differences.empty:
                logging.info(f"Found {len(differences)} records with stock differences")
                logging.info("Sample records with differences:")
                logging.info(differences.head(5)[['SKU', 'Plant', 'Target_Stock_Quantity', 'Current_PBI_Stock', 'Stock_Difference']].to_string())

            return result_df
        else:
            logging.error(f"Comparison file not found: {comparison_file}")
            return pd.DataFrame()
            
    except Exception as e:
        logging.error(f"Error loading comparison data: {e}")
        return pd.DataFrame()

if __name__ == '__main__':
    logging.info("=== TESTING COMPARISON DATA LOADING ===")
    comparison_df = load_comparison_data()
    
    if not comparison_df.empty:
        logging.info(f"✅ Successfully loaded {len(comparison_df)} records")
        
        # Show some statistics
        logging.info(f"Unique SKUs: {len(comparison_df['SKU'].unique())}")
        logging.info(f"Unique Plants: {len(comparison_df['Plant'].unique())}")
        logging.info(f"Target Stock range: {comparison_df['Target_Stock_Quantity'].min()} to {comparison_df['Target_Stock_Quantity'].max()}")
    else:
        logging.error("❌ Failed to load comparison data")
