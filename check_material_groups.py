import pandas as pd
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_excel_structure():
    """Check the structure of the Excel file to understand material group mappings"""
    try:
        excel_file = 'DE Temp POCM Calculation P07 2025_TL.xlsx'
        
        # Get all sheet names
        xl_file = pd.ExcelFile(excel_file)
        logging.info(f"Available sheets: {xl_file.sheet_names}")
        
        # Check each sheet for material group information
        for sheet_name in xl_file.sheet_names:
            try:
                df = pd.read_excel(excel_file, sheet_name=sheet_name, nrows=10)
                logging.info(f"\nSheet '{sheet_name}' columns: {list(df.columns)}")
                
                # Look for material group columns
                for col in df.columns:
                    if any(keyword in str(col).lower() for keyword in ['material', 'group', 'matkl', 'category']):
                        logging.info(f"  Found relevant column: '{col}'")
                        if len(df) > 0:
                            unique_values = df[col].dropna().unique()[:10]  # First 10 unique values
                            logging.info(f"    Sample values: {unique_values}")
                
            except Exception as e:
                logging.info(f"Could not read sheet '{sheet_name}': {e}")
        
        # Try to find the Glass and Others mapping
        try:
            # Look for a sheet that might contain the mapping
            for sheet_name in ['Glass and others', 'Category', 'Material Groups', 'Mapping']:
                if sheet_name in xl_file.sheet_names:
                    df = pd.read_excel(excel_file, sheet_name=sheet_name)
                    logging.info(f"\nFound mapping sheet '{sheet_name}':")
                    logging.info(f"Shape: {df.shape}")
                    logging.info(f"Columns: {list(df.columns)}")
                    logging.info("First 10 rows:")
                    logging.info(df.head(10).to_string())
                    return df
            
            # If no specific sheet found, check the first sheet
            df = pd.read_excel(excel_file, sheet_name=0)
            logging.info(f"\nChecking first sheet for material group data:")
            logging.info(f"Shape: {df.shape}")
            logging.info(f"Columns: {list(df.columns)}")
            
            # Look for material group column
            matkl_col = None
            for col in df.columns:
                if 'matkl' in str(col).lower() or 'material' in str(col).lower() and 'group' in str(col).lower():
                    matkl_col = col
                    break
            
            if matkl_col:
                logging.info(f"Found material group column: '{matkl_col}'")
                unique_groups = df[matkl_col].dropna().unique()
                logging.info(f"Unique material groups: {unique_groups}")
                
                # Show sample data
                sample_data = df[[col for col in df.columns if any(keyword in str(col).lower() 
                                for keyword in ['material', 'sku', 'matnr', 'group', 'category'])]].head(10)
                logging.info("Sample material group data:")
                logging.info(sample_data.to_string())
                
                return df
            
        except Exception as e:
            logging.error(f"Error reading Excel file: {e}")
        
        return pd.DataFrame()
        
    except Exception as e:
        logging.error(f"Error checking Excel structure: {e}")
        return pd.DataFrame()

if __name__ == '__main__':
    logging.info("=== CHECKING MATERIAL GROUP MAPPINGS ===")
    df = check_excel_structure()
    
    if not df.empty:
        logging.info("✅ Successfully found material group data")
    else:
        logging.info("❌ Could not find material group mappings")
