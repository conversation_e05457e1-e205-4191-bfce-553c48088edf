import pandas as pd
import logging
from databricks import sql

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_query(query: str, query_name: str, connection) -> pd.DataFrame:
    """Execute a SQL query and return results as DataFrame."""
    try:
        logging.info(f"Executing {query_name}...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        
        df = pd.DataFrame(data, columns=columns)
        logging.info(f"{query_name} completed. Fetched {len(df)} rows.")
        return df
    except Exception as e:
        logging.error(f"Error executing {query_name}: {e}")
        return pd.DataFrame()

def test_simple_opening_balance_for_test_sku(connection):
    """Test the opening balance calculation for our specific test SKU"""
    logging.info("\n" + "="*80)
    logging.info("TESTING OPENING BALANCE CALCULATION FOR SKU 7546978")
    logging.info("="*80)
    
    # Test query for our specific SKU
    query = """
    WITH TestSKU AS (
        SELECT '000000000007546978' AS Material, 'Glass' AS Category, '2024-11-01' AS StartDate
    ),
    
    OpeningBalance AS (
        SELECT 
            t.Material,
            t.Category,
            t.StartDate,
            mseg.werks AS Plant,
            mseg.lgort AS StorageLocation,
            SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS OpeningBalance,
            COUNT(*) AS MovementCount
        FROM TestSKU t
        JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg mseg
            ON t.Material = mseg.matnr
        WHERE mseg.werks = 'DE30'
          AND mseg.cpudt_mkpf < t.StartDate
        GROUP BY t.Material, t.Category, t.StartDate, mseg.werks, mseg.lgort
    ),
    
    MovementsAfterStart AS (
        SELECT 
            t.Material,
            t.Category,
            t.StartDate,
            mseg.werks AS Plant,
            mseg.lgort AS StorageLocation,
            SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE 0 END) AS Receipts,
            SUM(CASE WHEN mseg.shkzg = 'H' THEN mseg.menge ELSE 0 END) AS Issues,
            SUM(CASE WHEN mseg.shkzg = 'S' THEN mseg.menge ELSE -mseg.menge END) AS NetMovements,
            COUNT(*) AS MovementCount
        FROM TestSKU t
        JOIN brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg mseg
            ON t.Material = mseg.matnr
        WHERE mseg.werks = 'DE30'
          AND mseg.cpudt_mkpf >= t.StartDate
        GROUP BY t.Material, t.Category, t.StartDate, mseg.werks, mseg.lgort
    ),
    
    CurrentMARD AS (
        SELECT 
            matnr AS Material,
            werks AS Plant,
            lgort AS StorageLocation,
            labst AS CurrentStock
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
        WHERE matnr = '000000000007546978'
          AND werks = 'DE30'
    )
    
    SELECT 
        ob.Material AS SKU,
        ob.Plant,
        ob.StorageLocation,
        ob.Category,
        ob.StartDate,
        ob.OpeningBalance,
        mas.Receipts,
        mas.Issues,
        mas.NetMovements,
        ob.OpeningBalance + COALESCE(mas.NetMovements, 0) AS CalculatedStock,
        cm.CurrentStock AS MARDStock,
        (ob.OpeningBalance + COALESCE(mas.NetMovements, 0)) - cm.CurrentStock AS Difference,
        23070.0 AS TargetStock,
        (ob.OpeningBalance + COALESCE(mas.NetMovements, 0)) - 23070.0 AS DifferenceFromTarget
    FROM OpeningBalance ob
    LEFT JOIN MovementsAfterStart mas ON ob.Material = mas.Material 
        AND ob.Plant = mas.Plant 
        AND ob.StorageLocation = mas.StorageLocation
    LEFT JOIN CurrentMARD cm ON ob.Material = cm.Material 
        AND ob.Plant = cm.Plant 
        AND ob.StorageLocation = cm.StorageLocation
    ORDER BY ob.StorageLocation
    """
    
    result = execute_query(query, "Test Opening Balance Calculation", connection)
    
    if not result.empty:
        logging.info("Opening Balance Test Results:")
        for _, row in result.iterrows():
            logging.info(f"  Storage Location: {row['StorageLocation']}")
            logging.info(f"  Category: {row['Category']}")
            logging.info(f"  Start Date: {row['StartDate']}")
            logging.info(f"  Opening Balance: {float(row['OpeningBalance']):,.0f}")
            logging.info(f"  Receipts After Start: {float(row['Receipts']):,.0f}")
            logging.info(f"  Issues After Start: {float(row['Issues']):,.0f}")
            logging.info(f"  Net Movements After Start: {float(row['NetMovements']):,.0f}")
            logging.info(f"  CALCULATED STOCK: {float(row['CalculatedStock']):,.0f}")
            logging.info(f"  Current MARD Stock: {float(row['MARDStock']):,.0f}")
            logging.info(f"  Target Stock: {float(row['TargetStock']):,.0f}")
            logging.info(f"  Difference from Target: {float(row['DifferenceFromTarget']):+,.0f}")
            logging.info("")
            
            if abs(float(row['DifferenceFromTarget'])) < 10:
                logging.info("  ✅ SUCCESS! Calculated stock matches target!")
            elif abs(float(row['DifferenceFromTarget'])) < abs(float(row['MARDStock']) - 23070):
                logging.info("  ✅ IMPROVEMENT! Closer to target than MARD method")
            else:
                logging.info("  ❌ Still not matching target")
        
        # Sum across all storage locations
        total_calculated = result['CalculatedStock'].sum()
        total_mard = result['MARDStock'].sum()
        total_diff_from_target = total_calculated - 23070
        
        logging.info(f"TOTALS ACROSS ALL STORAGE LOCATIONS:")
        logging.info(f"  Total Calculated Stock: {total_calculated:,.0f}")
        logging.info(f"  Total MARD Stock: {total_mard:,.0f}")
        logging.info(f"  Total Difference from Target: {total_diff_from_target:+,.0f}")
        
        if abs(total_diff_from_target) < 10:
            logging.info("  ✅ TOTAL SUCCESS! Sum matches target!")
            return True
        
    else:
        logging.warning("No results from opening balance test")
    
    return False

def main():
    """Main debug function"""
    logging.info("=== DEBUGGING CORRECTED STOCK CALCULATION ===")
    
    try:
        connection = get_databricks_connection()
        
        # Test the opening balance calculation for our specific case
        success = test_simple_opening_balance_for_test_sku(connection)
        
        if success:
            logging.info("\n🎯 SOLUTION CONFIRMED!")
            logging.info("The opening balance + movements methodology works!")
            logging.info("The issue with the previous query was likely in the JOIN logic.")
        else:
            logging.info("\n❓ Need to investigate further")
            logging.info("The opening balance method may need refinement.")
        
        connection.close()
        
    except Exception as e:
        logging.error(f"Error during debug: {e}")

if __name__ == '__main__':
    main()
