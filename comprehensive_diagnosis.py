import pandas as pd
import logging
from databricks import sql

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_databricks_connection():
    """Establish and return a connection to the Databricks SQL warehouse."""
    try:
        server_hostname = "adb-671640162813626.6.azuredatabricks.net"
        http_path = "/sql/1.0/warehouses/b22260fd910a310c"
        access_token = "**************************************"
        
        logging.info("Establishing connection to Databricks...")
        connection = sql.connect(
            server_hostname=server_hostname,
            http_path=http_path,
            access_token=access_token
        )
        logging.info("Successfully connected to Databricks SQL warehouse.")
        return connection
        
    except Exception as e:
        logging.error(f"Failed to connect to Databricks: {e}")
        raise

def execute_query(query: str, query_name: str, connection) -> pd.DataFrame:
    """Execute a SQL query and return results as DataFrame."""
    try:
        logging.info(f"Executing {query_name}...")
        with connection.cursor() as cursor:
            cursor.execute(query)
            columns = [desc[0] for desc in cursor.description]
            data = cursor.fetchall()
        
        df = pd.DataFrame(data, columns=columns)
        logging.info(f"{query_name} completed. Fetched {len(df)} rows.")
        return df
    except Exception as e:
        logging.error(f"Error executing {query_name}: {e}")
        return pd.DataFrame()

def test_alternative_1_batch_stock(connection):
    """Alternative 1: Check if batch-specific stock (MCHB) is needed"""
    logging.info("\n" + "="*80)
    logging.info("ALTERNATIVE #1: Batch-Specific Stock (MCHB Table)")
    logging.info("="*80)
    
    query = """
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        lgort AS Storage_Location,
        charg AS Batch,
        clabs AS Unrestricted_Batch_Stock,
        cinsm AS QI_Batch_Stock,
        cspem AS Blocked_Batch_Stock,
        SUM(clabs + COALESCE(cinsm, 0) + COALESCE(cspem, 0)) OVER (PARTITION BY matnr, werks) AS Total_Batch_Stock,
        23070.0 AS Target_Stock,
        SUM(clabs + COALESCE(cinsm, 0) + COALESCE(cspem, 0)) OVER (PARTITION BY matnr, werks) - 23070.0 AS Difference_From_Target
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mchb
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
      AND (clabs > 0 OR cinsm > 0 OR cspem > 0)
    ORDER BY charg
    LIMIT 10
    """
    
    result = execute_query(query, "Alternative 1 - Batch Stock", connection)
    
    if not result.empty:
        total_batch_stock = float(result['Total_Batch_Stock'].iloc[0])
        diff = float(result['Difference_From_Target'].iloc[0])
        
        logging.info(f"Batch-specific stock analysis:")
        logging.info(f"  Total Batch Stock: {total_batch_stock:,.0f} units")
        logging.info(f"  Difference from Target: {diff:+,.0f}")
        
        if abs(diff) < 10.0:
            logging.info("✅ MATCH FOUND! Batch-specific stock explains the difference")
            return True
        else:
            logging.info("❌ Batch stock doesn't match target")
            
        # Show batch details
        logging.info("Batch breakdown:")
        for _, row in result.head(5).iterrows():
            logging.info(f"  Batch {row['Batch']}: {float(row['Unrestricted_Batch_Stock']):,.0f} units")
    else:
        logging.info("❌ No batch stock data found")
    
    return False

def test_alternative_2_special_stock(connection):
    """Alternative 2: Check special stock types (MSKA)"""
    logging.info("\n" + "="*80)
    logging.info("ALTERNATIVE #2: Special Stock (MSKA Table)")
    logging.info("="*80)
    
    query = """
    SELECT 
        matnr AS SKU,
        werks AS Plant,
        lgort AS Storage_Location,
        sobkz AS Special_Stock_Indicator,
        kalab AS Unrestricted_Special_Stock,
        kains AS QI_Special_Stock,
        kaspe AS Blocked_Special_Stock,
        SUM(kalab + COALESCE(kains, 0) + COALESCE(kaspe, 0)) AS Total_Special_Stock,
        23070.0 AS Target_Stock,
        SUM(kalab + COALESCE(kains, 0) + COALESCE(kaspe, 0)) - 23070.0 AS Difference_From_Target
    FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mska
    WHERE matnr = '000000000007546978'
      AND werks = 'DE30'
    GROUP BY matnr, werks, lgort, sobkz, kalab, kains, kaspe
    """
    
    result = execute_query(query, "Alternative 2 - Special Stock", connection)
    
    if not result.empty:
        total_special = float(result['Total_Special_Stock'].sum())
        diff = total_special - 23070.0
        
        logging.info(f"Special stock analysis:")
        logging.info(f"  Total Special Stock: {total_special:,.0f} units")
        logging.info(f"  Difference from Target: {diff:+,.0f}")
        
        if abs(diff) < 10.0:
            logging.info("✅ MATCH FOUND! Special stock explains the difference")
            return True
        else:
            logging.info("❌ Special stock doesn't match target")
    else:
        logging.info("❌ No special stock data found")
    
    return False

def test_alternative_3_combined_sources(connection):
    """Alternative 3: Combine MARD + MCHB + MSKA"""
    logging.info("\n" + "="*80)
    logging.info("ALTERNATIVE #3: Combined Stock Sources (MARD + MCHB + MSKA)")
    logging.info("="*80)
    
    query = """
    WITH mard_stock AS (
        SELECT 
            matnr, werks,
            SUM(labst + COALESCE(insme, 0) + COALESCE(speme, 0) + COALESCE(umlme, 0)) AS mard_total
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mard
        WHERE matnr = '000000000007546978' AND werks = 'DE30'
        GROUP BY matnr, werks
    ),
    batch_stock AS (
        SELECT 
            matnr, werks,
            SUM(clabs + COALESCE(cinsm, 0) + COALESCE(cspem, 0)) AS batch_total
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mchb
        WHERE matnr = '000000000007546978' AND werks = 'DE30'
        GROUP BY matnr, werks
    ),
    special_stock AS (
        SELECT 
            matnr, werks,
            SUM(kalab + COALESCE(kains, 0) + COALESCE(kaspe, 0)) AS special_total
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mska
        WHERE matnr = '000000000007546978' AND werks = 'DE30'
        GROUP BY matnr, werks
    )
    SELECT 
        COALESCE(m.matnr, b.matnr, s.matnr) AS SKU,
        COALESCE(m.werks, b.werks, s.werks) AS Plant,
        COALESCE(m.mard_total, 0) AS MARD_Stock,
        COALESCE(b.batch_total, 0) AS Batch_Stock,
        COALESCE(s.special_total, 0) AS Special_Stock,
        COALESCE(m.mard_total, 0) + COALESCE(b.batch_total, 0) + COALESCE(s.special_total, 0) AS Combined_Total,
        23070.0 AS Target_Stock,
        (COALESCE(m.mard_total, 0) + COALESCE(b.batch_total, 0) + COALESCE(s.special_total, 0)) - 23070.0 AS Difference_From_Target
    FROM mard_stock m
    FULL OUTER JOIN batch_stock b ON m.matnr = b.matnr AND m.werks = b.werks
    FULL OUTER JOIN special_stock s ON COALESCE(m.matnr, b.matnr) = s.matnr AND COALESCE(m.werks, b.werks) = s.werks
    """
    
    result = execute_query(query, "Alternative 3 - Combined Sources", connection)
    
    if not result.empty:
        row = result.iloc[0]
        mard_stock = float(row['MARD_Stock'])
        batch_stock = float(row['Batch_Stock'])
        special_stock = float(row['Special_Stock'])
        combined_total = float(row['Combined_Total'])
        diff = float(row['Difference_From_Target'])
        
        logging.info(f"Combined stock analysis:")
        logging.info(f"  MARD Stock: {mard_stock:,.0f} units")
        logging.info(f"  Batch Stock: {batch_stock:,.0f} units")
        logging.info(f"  Special Stock: {special_stock:,.0f} units")
        logging.info(f"  COMBINED TOTAL: {combined_total:,.0f} units")
        logging.info(f"  Difference from Target: {diff:+,.0f}")
        
        if abs(diff) < 10.0:
            logging.info("✅ MATCH FOUND! Combined sources explain the target")
            return True
        else:
            logging.info("❌ Combined sources don't match target")
    else:
        logging.info("❌ No combined data found")
    
    return False

def test_alternative_4_different_date(connection):
    """Alternative 4: Check if target is from a specific date in MSEG"""
    logging.info("\n" + "="*80)
    logging.info("ALTERNATIVE #4: Stock at Specific Date (MSEG Cumulative)")
    logging.info("="*80)
    
    # Test different cutoff dates
    test_dates = ['2025-07-31', '2025-06-30', '2025-05-31', '2025-04-30']
    
    for test_date in test_dates:
        query = f"""
        SELECT 
            '{test_date}' AS Cutoff_Date,
            SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) AS Stock_At_Date,
            COUNT(*) AS Movement_Count,
            23070.0 AS Target_Stock,
            SUM(CASE WHEN shkzg = 'S' THEN menge ELSE -menge END) - 23070.0 AS Difference_From_Target
        FROM brewdat_uc_europe_prod.slv_eur_tech_sap_ecc_europe.mseg
        WHERE matnr = '000000000007546978'
          AND werks = 'DE30'
          AND cpudt_mkpf <= '{test_date}'
        """
        
        result = execute_query(query, f"Stock at {test_date}", connection)
        
        if not result.empty:
            row = result.iloc[0]
            stock_at_date = float(row['Stock_At_Date'])
            diff = float(row['Difference_From_Target'])
            count = int(row['Movement_Count'])
            
            logging.info(f"  {test_date}: {stock_at_date:,.0f} units ({count:,} movements, diff: {diff:+,.0f})")
            
            if abs(diff) < 10.0:
                logging.info(f"    ✅ MATCH FOUND! Target matches stock as of {test_date}")
                return True
    
    logging.info("❌ No date-specific matches found")
    return False

def main():
    """Main comprehensive diagnostic function"""
    logging.info("=== COMPREHENSIVE SAP STOCK RECONCILIATION DIAGNOSIS ===")
    logging.info("SKU: 000000000007546978 at Plant DE30")
    logging.info("Current Script: 22,758 units (MARD LABST only)")
    logging.info("Target (Ground Truth): 23,070 units")
    logging.info("Missing: 312 units")
    logging.info("")
    logging.info("Testing alternative data sources and methods...")
    
    try:
        connection = get_databricks_connection()
        
        # Test all alternatives
        alt1_match = test_alternative_1_batch_stock(connection)
        alt2_match = test_alternative_2_special_stock(connection)
        alt3_match = test_alternative_3_combined_sources(connection)
        alt4_match = test_alternative_4_different_date(connection)
        
        # Final summary
        logging.info("\n" + "="*80)
        logging.info("COMPREHENSIVE DIAGNOSIS SUMMARY")
        logging.info("="*80)
        logging.info("Original Hypotheses:")
        logging.info("  Hypothesis 1 (Stock Types): ❌ NO - All MARD types = 22,758")
        logging.info("  Hypothesis 2 (Historical): ❌ NO - Per your feedback")
        logging.info("  Hypothesis 3 (Movements): ❌ NO - Per your feedback")
        logging.info("")
        logging.info("Alternative Methods:")
        logging.info(f"  Alternative 1 (Batch Stock): {'✅ YES' if alt1_match else '❌ NO'}")
        logging.info(f"  Alternative 2 (Special Stock): {'✅ YES' if alt2_match else '❌ NO'}")
        logging.info(f"  Alternative 3 (Combined Sources): {'✅ YES' if alt3_match else '❌ NO'}")
        logging.info(f"  Alternative 4 (Date-Specific): {'✅ YES' if alt4_match else '❌ NO'}")
        
        # Provide solution
        if alt1_match:
            logging.info("\n🎯 SOLUTION FOUND: Use MCHB (Batch Stock) table")
            logging.info("   The target includes batch-managed stock quantities")
        elif alt2_match:
            logging.info("\n🎯 SOLUTION FOUND: Use MSKA (Special Stock) table")
            logging.info("   The target includes special stock categories")
        elif alt3_match:
            logging.info("\n🎯 SOLUTION FOUND: Combine MARD + MCHB + MSKA")
            logging.info("   The target requires all stock sources combined")
        elif alt4_match:
            logging.info("\n🎯 SOLUTION FOUND: Use date-specific MSEG calculation")
            logging.info("   The target represents stock at a specific historical date")
        else:
            logging.info("\n❓ SOLUTION NOT YET FOUND")
            logging.info("   Additional areas to investigate:")
            logging.info("   - Consignment stock (MSLB table)")
            logging.info("   - Project stock")
            logging.info("   - Different material number variants")
            logging.info("   - Cross-plant stock transfers")
            logging.info("   - Manual adjustments or corrections")
        
        connection.close()
        
    except Exception as e:
        logging.error(f"Error during comprehensive diagnosis: {e}")

if __name__ == '__main__':
    main()
